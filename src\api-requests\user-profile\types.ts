import { UserCV } from '@/store/user-atom';
import {
  ApplicationStatus,
  JobCandidateClosedStat,
  JobCandidateHiredStat,
  JobCandidateInterviewStat,
  JobCandidateOfferStat,
  JobCandidateRejectedStat,
} from '../job-candidate/types';
import { CandidateQuickQuestion } from '../job/process-cv-to-apply';

export const UserProfileQueryKeys = {
  GET_USER_PROFILE: 'getUserProfile',
  GET_USER_APPLICATIONS: 'getUserApplications',
  UPSERT_USER_PROFILE: 'upsertUserProfile',
};

export interface UpdateUserProfileParams {
  firstName: string;
  lastName: string;
  userId: string;
  file?: File | null;
  address?: string;
  phone?: string;
  city?: string;
  region?: string;
  country?: string;
  gender?: string;
}

export interface UserProfile {
  _id: string;
  id: string;
  firstName: string;
  lastName: string;
  fullName: string;
  userId: string;
  email: string;
  avatar?: string;
  address?: string;
  phone?: string;
  city?: string;
  region?: string;
  country?: string;
  gender?: string;
  cv?: UserCV;
  location: string;
  skills?: string[];
  industries?: string[];
  levels?: string[];
  workTypes?: string[];
  workPlaces?: string[];
  expectedSalary?: {
    min?: number;
    max?: number;
    currency?: string;
    period?: string;
  };
  experience?: {
    min?: number;
    max?: number;
  };
}

export interface UserApplication {
  _id: string;
  jobId?: string;
  simulationId?: string;
  orgId?: string;
  userId: string;
  applyMode: 'cv' | 'simulation';
  matchPercentage?: number;
  applicationStatus: ApplicationStatus;
  simulationStatus?: string;
  quickQuestions?: CandidateQuickQuestion[];
  job?: any;
  simulation?: any;
  org?: any;
  appliedAt?: string;
  withdrawnAt?: string;
  interviewStat?: JobCandidateInterviewStat;
  offerStat?: JobCandidateOfferStat;
  hiredStat?: JobCandidateHiredStat;
  rejectedStat?: JobCandidateRejectedStat;
  closedStat?: JobCandidateClosedStat;
}

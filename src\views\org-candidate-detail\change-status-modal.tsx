'use client';

import { ApplicationStatus } from '@/api-requests/job-candidate/types';
import { SelectOption } from '@/api-requests/types';
import currenciesData from '@/data/currencies.json';
import timezonesData from '@/data/timezones.json';
import useApplicationStatus from '@/hooks/use-application-status';
import DatePicker from '@/shared/date-picker';
import { convertApplicationStatus } from '@/utils/application-simulation-status';
import cn from '@/utils/class-names';
import { getStatusReasonCodes } from '@/utils/reason-codes';
import { addHours } from 'date-fns';
import { X } from 'lucide-react';
import { useEffect, useMemo, useState } from 'react';
import { createPortal } from 'react-dom';
import { Controller, useForm } from 'react-hook-form';
import { Badge, Button, Input, Modal, Select, Textarea } from 'rizzui';

// Types and interfaces
interface ChangeStatusModalProps {
  open: boolean;
  onClose: () => void;
  currentStatus: ApplicationStatus;
  candidateName: string;
  jobTitle: string;
  onSubmit: (data: StatusChangeFormData) => void;
  actionType?: string; // New prop to determine the specific action
  targetStatus?: ApplicationStatus; // New prop for pre-selected target status
}

export interface StatusChangeFormData {
  targetStatus: SelectOption | null;
  reasonCode?: SelectOption | null;
  notes?: string;
  // skipJustification?: string;
  // Interview specific
  // roundNo?: number;
  scheduledAt?: Date;
  timezone?: SelectOption | null;
  // location?: string;
  // meetingLink?: string;
  respondDeadline?: Date;
  // Offer specific
  jobTitleLevel?: string;
  startDate?: Date;
  offerExpiresAt?: Date;
  offerAmount?: number;
  currency?: (SelectOption & { [key: string]: any }) | null;
  // compensationPackage?: string;
  // Hired specific
  employmentType?: SelectOption | null;
}

const statusOptions: SelectOption[] = [
  { label: 'Draft', value: ApplicationStatus.DRAFT },
  { label: 'Submitted', value: ApplicationStatus.SUBMITTED },
  { label: 'Under Review', value: ApplicationStatus.UNDER_REVIEW },
  { label: 'Interview', value: ApplicationStatus.INTERVIEW },
  { label: 'Rejected', value: ApplicationStatus.REJECTED },
  { label: 'Offer', value: ApplicationStatus.OFFER },
  { label: 'Hired', value: ApplicationStatus.HIRED },
  { label: 'Closed', value: ApplicationStatus.CLOSED },
];

const employmentTypeOptions: SelectOption[] = [
  { label: 'Full-time', value: 'full_time' },
  { label: 'Part-time', value: 'part_time' },
  { label: 'Contract', value: 'contract' },
];

const timezoneOptions: SelectOption[] = timezonesData.map((timezone) => ({
  label: `${timezone.label} - ${timezone.id}`,
  value: timezone.id,
}));

const currencyOptions: (SelectOption & { [key: string]: any })[] =
  currenciesData.map((currency) => ({
    label: `${currency.code} - ${currency.name}`,
    value: currency.code,
    symbol: currency.symbol,
    fractionDigits: currency.fractionDigits,
  }));

const getActionTitle = (actionType: string): string => {
  switch (actionType) {
    case 'move_to_under_review':
      return 'Move to Under Review';
    case 'interview.scheduled':
      return 'Schedule Interview';
    case 'interview.rescheduled':
      return 'Reschedule Interview';
    case 'interview.completed':
      return 'Mark Interview as Completed';
    case 'interview.canceled_by_interviewer':
      return 'Cancel Interview';
    case 'create_offer':
      return 'Create Offer';
    case 'update_offer':
      return 'Update Offer';
    case 'offer.accepted':
      return 'Mark Offer as Accepted';
    case 'offer.declined':
      return 'Mark Offer as Declined';
    case 'offer.rescinded':
      return 'Rescind Offer';
    case 'mark_hired':
      return 'Mark as Hired';
    case 'status.rejected':
      return 'Reject Application';
    case 'status.closed':
      return 'Close Application';
    default:
      return 'Change Status';
  }
};

const getActionButtonText = (actionType: string): string => {
  switch (actionType) {
    case 'move_to_under_review':
      return 'Move to Review';
    case 'interview.scheduled':
      return 'Schedule';
    case 'interview.rescheduled':
      return 'Reschedule';
    case 'interview.completed':
      return 'Mark Completed';
    case 'interview.canceled_by_interviewer':
      return 'Cancel';
    case 'create_offer':
      return 'Create Offer';
    case 'update_offer':
      return 'Update Offer';
    case 'offer.accepted':
      return 'Mark Accepted';
    case 'offer.declined':
      return 'Mark Declined';
    case 'offer.rescinded':
      return 'Rescind';
    case 'mark_hired':
      return 'Mark Hired';
    case 'status.rejected':
      return 'Reject';
    case 'status.closed':
      return 'Close';
    default:
      return 'Save';
  }
};

export default function ChangeStatusModal({
  open,
  onClose,
  currentStatus,
  candidateName,
  jobTitle,
  onSubmit,
  actionType,
  targetStatus: propTargetStatus,
}: ChangeStatusModalProps) {
  const { getApplicationStatusClassName } = useApplicationStatus();

  const [selectedStatus, setSelectedStatus] =
    useState<ApplicationStatus | null>(null);

  const {
    control,
    handleSubmit,
    watch,
    setValue,
    reset,
    formState: { errors },
  } = useForm<StatusChangeFormData>({
    defaultValues: {
      targetStatus:
        statusOptions.find((option) => option.value === currentStatus) || null,
      reasonCode: null,
      currency: {
        value: 'USD',
        label: 'USD - US dollar',
        symbol: 'US$',
        fractionDigits: 2,
      },
      offerAmount: 1000,
    },
  });

  // Get available status options based on current status
  const availableStatusOptions = useMemo(() => {
    // If we have a specific target status from action, use that
    if (propTargetStatus) {
      return statusOptions.filter(
        (option) => option.value === propTargetStatus
      );
    }

    const nonChangeableStatuses = [
      ApplicationStatus.DRAFT,
      ApplicationStatus.HIRED,
      ApplicationStatus.REJECTED,
      ApplicationStatus.CLOSED,
    ];

    if (nonChangeableStatuses.includes(currentStatus)) {
      return [];
    }

    return statusOptions;
    // return statusOptions.filter((option) => option.value !== currentStatus);
  }, [currentStatus, propTargetStatus]);

  // Get reason codes for selected status
  const availableRejectedReasonCodes = useMemo(() => {
    if (!selectedStatus && !actionType) return [];

    return getStatusReasonCodes(selectedStatus, actionType).map((code) => ({
      label: code.label,
      value: code.code,
    }));
  }, [selectedStatus, actionType]);

  // Handle form submission
  const onFormSubmit = (data: StatusChangeFormData) => {
    onSubmit(data);
  };

  // Handle status change
  const handleStatusChange = (option: SelectOption) => {
    const newStatus = option.value as ApplicationStatus;
    setSelectedStatus(newStatus);
    setValue('targetStatus', option);
  };

  // Reset form when modal opens/closes
  useEffect(() => {
    if (open) {
      const initialTargetStatus = propTargetStatus || currentStatus;
      reset({
        targetStatus:
          statusOptions.find(
            (option) => option.value === initialTargetStatus
          ) || null,
        timezone:
          timezoneOptions.find((option) => option.value === 'Etc/UTC') || null,
        reasonCode: null,
        currency: {
          value: 'USD',
          label: 'USD - US dollar',
          symbol: 'US$',
          fractionDigits: 2,
        },
        offerAmount: 1000,
        jobTitleLevel: jobTitle,
        employmentType: employmentTypeOptions[0],
      });
      setSelectedStatus(propTargetStatus || null);
    }
  }, [open, currentStatus, propTargetStatus, reset, jobTitle]);

  // Show warning for non-changeable statuses
  if (availableStatusOptions.length === 0) {
    return (
      <Modal isOpen={open} onClose={onClose} size="lg" customSize="600px">
        <div className="p-6">
          <div className="mb-4 flex items-center justify-between">
            <h2 className="text-lg font-semibold">Change status</h2>
            <button
              onClick={onClose}
              className="rounded-lg p-1 hover:bg-gray-100"
            >
              <X className="h-5 w-5" />
            </button>
          </div>
          <div className="text-center">
            <p className="text-gray-600">
              Status cannot be changed from{' '}
              <span className="font-semibold">{currentStatus}</span>.
            </p>
            <Button
              onClick={onClose}
              variant="solid"
              className="mt-4 text-white"
            >
              OK
            </Button>
          </div>
        </div>
      </Modal>
    );
  }

  return (
    <Modal
      isOpen={open}
      onClose={onClose}
      size="lg"
      // customSize="600px"
      // className="[&_.overflow-hidden[data-headlessui-state=open]]:!overflow-visible"
      containerClassName="w-auto lg:w-[600px]"
    >
      <div className="max-h-[80vh] overflow-y-auto p-6">
        <div className="mb-6 flex items-center justify-between">
          <h2 className="text-lg font-semibold">
            {actionType ? getActionTitle(actionType) : 'Change status'}
          </h2>
          <button
            onClick={onClose}
            className="rounded-lg p-1 hover:bg-gray-100"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        <div className="mb-6">
          <div className="text-sm text-gray-500">
            <p>
              {candidateName} - {jobTitle}
            </p>
            <p>
              Current status:{' '}
              <span className="font-bold">
                {currentStatus === ApplicationStatus.UNDER_REVIEW
                  ? 'Under Review'
                  : currentStatus.charAt(0).toUpperCase() +
                    currentStatus.slice(1)}
              </span>
            </p>
          </div>
        </div>

        <form onSubmit={handleSubmit(onFormSubmit)} className="space-y-6">
          {/* Target Status - only show if no specific action type */}
          {!actionType && (
            <div>
              <label className="mb-2 block text-sm font-medium">
                Target status
              </label>
              <div className="flex items-center gap-3">
                <Controller
                  name="targetStatus"
                  control={control}
                  rules={{ required: 'Please select a status' }}
                  render={({ field }) => (
                    <Select
                      {...field}
                      options={availableStatusOptions}
                      placeholder="Select status"
                      className="flex-1"
                      onChange={handleStatusChange}
                    />
                  )}
                />
                {selectedStatus && (
                  <>
                    <Badge
                      className={cn(
                        getApplicationStatusClassName(selectedStatus)
                      )}
                    >
                      {convertApplicationStatus(selectedStatus)}
                    </Badge>
                  </>
                )}
              </div>
              {errors.targetStatus && (
                <p className="mt-1 text-sm text-red-600">
                  {errors.targetStatus.message}
                </p>
              )}
            </div>
          )}

          {/* Show target status as read-only when action type is specified */}
          {actionType && selectedStatus && (
            <div>
              {/* <label className="mb-2 block text-sm font-medium">
                Action
              </label> */}
              <div className="flex items-center gap-3">
                <Badge
                  className={cn(getApplicationStatusClassName(selectedStatus))}
                >
                  {convertApplicationStatus(selectedStatus)}
                </Badge>
              </div>
            </div>
          )}

          {/* Preflight warnings */}
          {/* {selectedStatus === ApplicationStatus.OFFER && (
            <div className="rounded-lg border border-yellow-200 bg-yellow-50 p-4">
              <div className="flex items-start gap-2">
                <div className="mt-0.5 text-yellow-600">⚠</div>
                <div>
                  <h4 className="font-medium text-yellow-800">
                    Preflight warnings
                  </h4>
                  <ul className="mt-1 list-inside list-disc text-sm text-yellow-700">
                    <li>
                      Candidate already has an active Offer in this
                      organization.
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          )}

          {selectedStatus === ApplicationStatus.INTERVIEW && (
            <div className="rounded-lg border border-yellow-200 bg-yellow-50 p-4">
              <div className="flex items-start gap-2">
                <div className="mt-0.5 text-yellow-600">⚠</div>
                <div>
                  <h4 className="font-medium text-yellow-800">
                    Preflight warnings
                  </h4>
                  <ul className="mt-1 list-inside list-disc text-sm text-yellow-700">
                    <li>
                      Candidate already has an active Offer in this
                      organization.
                    </li>
                    <li>
                      Candidate has other interview slots in this org. Check for
                      time conflicts.
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          )} */}

          {/* Rejected Form */}
          {(selectedStatus === ApplicationStatus.REJECTED ||
            actionType === 'status.rejected') && (
            <>
              <div>
                <label className="mb-2 block text-sm font-medium">
                  Reason code
                </label>
                <Controller
                  name="reasonCode"
                  control={control}
                  rules={{ required: 'Please select a reason' }}
                  render={({ field }) => (
                    <Select
                      {...field}
                      options={availableRejectedReasonCodes}
                      placeholder="Select reason"
                      className="w-full"
                    />
                  )}
                />
                {errors.reasonCode && (
                  <p className="mt-1 text-sm text-red-600">
                    {errors.reasonCode.message}
                  </p>
                )}
              </div>

              <div>
                <label className="mb-2 block text-sm font-medium">
                  Notes (optional)
                </label>
                <Controller
                  name="notes"
                  control={control}
                  render={({ field }) => (
                    <Textarea
                      {...field}
                      placeholder="Add context for this decision"
                      rows={3}
                      className="w-full"
                    />
                  )}
                />
              </div>

              {/* <div>
                <label className="mb-2 block text-sm font-medium">
                  Skip justification
                </label>
                <Controller
                  name="skipJustification"
                  control={control}
                  render={({ field }) => (
                    <Textarea
                      {...field}
                      placeholder="Referral/rehire/direct manager request..."
                      rows={3}
                      className="w-full"
                    />
                  )}
                />
              </div> */}
            </>
          )}

          {/* Hired Form */}
          {(selectedStatus === ApplicationStatus.HIRED ||
            actionType === 'mark_hired') && (
            <>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="mb-2 block text-sm font-medium">
                    Start date
                  </label>
                  <Controller
                    name="startDate"
                    control={control}
                    rules={{ required: 'Please select start date' }}
                    render={({ field }) => (
                      <DatePicker
                        selected={field.value}
                        onChange={field.onChange}
                        placeholderText="mm / dd / yyyy"
                        inputProps={{
                          className: 'w-full',
                        }}
                        popperContainer={({ children: popperChildren }) =>
                          createPortal(popperChildren, document.body)
                        }
                        popperClassName="!z-[999]"
                        shouldCloseOnSelect
                        minDate={new Date()}
                      />
                    )}
                  />
                  {errors.startDate && (
                    <p className="mt-1 text-sm text-red-600">
                      {errors.startDate.message}
                    </p>
                  )}
                </div>

                <div>
                  <label className="mb-2 block text-sm font-medium">
                    Employment type
                  </label>
                  <Controller
                    name="employmentType"
                    control={control}
                    rules={{ required: 'Please select employment type' }}
                    render={({ field }) => (
                      <Select
                        {...field}
                        options={employmentTypeOptions}
                        placeholder="Select"
                        className="w-full"
                      />
                    )}
                  />
                  {errors.employmentType && (
                    <p className="mt-1 text-sm text-red-600">
                      {errors.employmentType.message}
                    </p>
                  )}
                </div>
              </div>
              <div>
                <div>
                  <label className="mb-2 block text-sm font-medium">
                    Timezone
                  </label>
                  <Controller
                    name="timezone"
                    control={control}
                    render={({ field }) => (
                      <Select
                        {...field}
                        options={timezoneOptions}
                        searchable={true}
                        placeholder="Select timezone"
                        className="flex-1"
                      />
                    )}
                  />
                </div>
              </div>

              {/* <div>
                <label className="mb-2 block text-sm font-medium">
                  Skip justification
                </label>
                <Controller
                  name="skipJustification"
                  control={control}
                  render={({ field }) => (
                    <Textarea
                      {...field}
                      placeholder="Referral/rehire/direct manager request..."
                      rows={3}
                      className="w-full"
                    />
                  )}
                />
              </div> */}
            </>
          )}

          {/* Offer Form */}
          {(selectedStatus === ApplicationStatus.OFFER ||
            actionType === 'create_offer' ||
            actionType === 'update_offer') && (
            <>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="mb-2 block text-sm font-medium">
                    Job title / level
                  </label>
                  <Controller
                    name="jobTitleLevel"
                    control={control}
                    render={({ field }) => (
                      <Input
                        {...field}
                        placeholder="Senior Frontend Engineer"
                        className="w-full"
                      />
                    )}
                  />
                </div>

                <div>
                  <label className="mb-2 block text-sm font-medium">
                    Start date
                  </label>
                  <Controller
                    name="startDate"
                    control={control}
                    render={({ field }) => (
                      <DatePicker
                        selected={field.value}
                        onChange={field.onChange}
                        placeholderText="mm / dd / yyyy"
                        inputProps={{
                          className: 'w-full',
                        }}
                        popperContainer={({ children: popperChildren }) =>
                          createPortal(popperChildren, document.body)
                        }
                        popperClassName="!z-[999]"
                        shouldCloseOnSelect
                        minDate={new Date()}
                      />
                    )}
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="mb-2 block text-sm font-medium">
                    Offer expires at
                  </label>
                  <Controller
                    name="offerExpiresAt"
                    control={control}
                    render={({ field }) => (
                      <DatePicker
                        selected={field.value}
                        onChange={field.onChange}
                        placeholderText="mm / dd / yyyy"
                        inputProps={{
                          className: 'w-full',
                        }}
                        popperContainer={({ children: popperChildren }) =>
                          createPortal(popperChildren, document.body)
                        }
                        popperClassName="!z-[999]"
                        shouldCloseOnSelect
                        maxDate={addHours(
                          watch('startDate') || new Date(),
                          -24
                        )}
                        minDate={new Date()}
                        disabled={!!watch('startDate') ? false : true}
                        isClearable
                        // clearButtonClassName="!right-[6px]"
                      />
                    )}
                  />
                </div>
                <div>
                  <label className="mb-2 block text-sm font-medium">
                    Timezone
                  </label>
                  <Controller
                    name="timezone"
                    control={control}
                    render={({ field }) => (
                      <Select
                        {...field}
                        options={timezoneOptions}
                        searchable={true}
                        placeholder="Select timezone"
                        className="flex-1"
                      />
                    )}
                  />
                </div>
              </div>

              <div>
                <label className="mb-2 block text-sm font-medium">
                  Compensation
                </label>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Controller
                      name="offerAmount"
                      control={control}
                      render={({ field }) => (
                        <Input
                          {...field}
                          type="number"
                          min="1"
                          value={field.value || 1}
                          onChange={(e) =>
                            field.onChange(parseInt(e.target.value) || 1)
                          }
                          className="w-full"
                        />
                      )}
                    />
                  </div>
                  <div>
                    <Controller
                      name="currency"
                      control={control}
                      render={({ field }) => (
                        <Select
                          {...field}
                          options={currencyOptions}
                          searchable={true}
                          placeholder="Select currency"
                          className="flex-1"
                        />
                      )}
                    />
                  </div>
                </div>
              </div>

              {/* <div>
                <label className="mb-2 block text-sm font-medium">
                  Skip justification
                </label>
                <Controller
                  name="skipJustification"
                  control={control}
                  render={({ field }) => (
                    <Textarea
                      {...field}
                      placeholder="Referral/rehire/direct manager request..."
                      rows={3}
                      className="w-full"
                    />
                  )}
                />
              </div> */}
            </>
          )}

          {/* Interview Form */}
          {(selectedStatus === ApplicationStatus.INTERVIEW ||
            actionType === 'interview.scheduled' ||
            actionType === 'interview.rescheduled') && (
            <>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <div>
                    <label className="mb-2 block text-sm font-medium">
                      Scheduled at
                    </label>
                    <Controller
                      name="scheduledAt"
                      control={control}
                      rules={{ required: 'Please select scheduled at' }}
                      render={({ field }) => (
                        <DatePicker
                          selected={field.value}
                          onChange={field.onChange}
                          placeholderText="mm / dd / yyyy , --:-- --"
                          showTimeSelect
                          timeFormat="HH:mm"
                          timeIntervals={15}
                          dateFormat="MM/dd/yyyy, HH:mm"
                          inputProps={{
                            className: 'w-full',
                          }}
                          popperContainer={({ children: popperChildren }) =>
                            createPortal(popperChildren, document.body)
                          }
                          popperClassName="!z-[999]"
                          shouldCloseOnSelect
                          minDate={new Date()}
                        />
                      )}
                    />
                  </div>

                  {!!errors.scheduledAt && (
                    <p className="mt-1 text-sm text-red-600">
                      {errors.scheduledAt.message}
                    </p>
                  )}
                </div>

                <div>
                  <label className="mb-2 block text-sm font-medium">
                    Respond deadline
                  </label>
                  <Controller
                    name="respondDeadline"
                    control={control}
                    render={({ field }) => (
                      <DatePicker
                        selected={field.value}
                        onChange={field.onChange}
                        placeholderText="mm / dd / yyyy , --:-- --"
                        showTimeSelect
                        timeFormat="HH:mm"
                        timeIntervals={15}
                        dateFormat="MM/dd/yyyy, HH:mm"
                        inputProps={{
                          className: 'w-full',
                        }}
                        popperContainer={({ children: popperChildren }) =>
                          createPortal(popperChildren, document.body)
                        }
                        popperClassName="!z-[999]"
                        shouldCloseOnSelect
                        minDate={new Date()}
                        maxDate={addHours(
                          watch('scheduledAt') || new Date(),
                          -24
                        )}
                        isClearable
                        disabled={!!watch('scheduledAt') ? false : true}
                        // clearButtonClassName="!right-[6px]"
                        // TODO minTime, maxTime
                      />
                    )}
                  />
                </div>
              </div>
              <div>
                {/* TODO: default org timezone */}
                <label className="mb-2 block text-sm font-medium">
                  Timezone
                </label>
                <Controller
                  name="timezone"
                  control={control}
                  render={({ field }) => (
                    <Select
                      {...field}
                      options={timezoneOptions}
                      searchable={true}
                      placeholder="Select timezone"
                      className="flex-1"
                    />
                  )}
                />
              </div>
              <div>
                <label className="mb-2 block text-sm font-medium">
                  Notes (optional)
                </label>
                <Controller
                  name="notes"
                  control={control}
                  render={({ field }) => (
                    <Textarea
                      {...field}
                      placeholder="Location, Meeting link, etc."
                      rows={3}
                      className="w-full"
                    />
                  )}
                />
              </div>
            </>
          )}

          {/* Interview Completion Form */}
          {actionType === 'interview.completed' && (
            <>
              <div>
                <label className="mb-2 block text-sm font-medium">
                  Interview Notes
                </label>
                <Controller
                  name="notes"
                  control={control}
                  render={({ field }) => (
                    <Textarea
                      {...field}
                      placeholder="Add notes about the interview"
                      rows={3}
                      className="w-full"
                    />
                  )}
                />
              </div>

              <div>
                <label className="mb-2 block text-sm font-medium">
                  Reason code (optional)
                </label>
                <Controller
                  name="reasonCode"
                  control={control}
                  render={({ field }) => (
                    <Select
                      {...field}
                      options={availableRejectedReasonCodes}
                      placeholder="Select reason"
                      className="w-full"
                    />
                  )}
                />
              </div>
            </>
          )}

          {/* Interview Accept/Decline Form */}
          {(actionType === 'offer.accepted' ||
            actionType === 'offer.declined') && (
            <>
              <div>
                <label className="mb-2 block text-sm font-medium">Notes</label>
                <Controller
                  name="notes"
                  control={control}
                  render={({ field }) => (
                    <Textarea
                      {...field}
                      placeholder="Add context for this decision"
                      rows={3}
                      className="w-full"
                    />
                  )}
                />
              </div>

              {actionType === 'offer.declined' && (
                <div>
                  <label className="mb-2 block text-sm font-medium">
                    Reason code
                  </label>
                  <Controller
                    name="reasonCode"
                    control={control}
                    rules={{ required: 'Please select a reason' }}
                    render={({ field }) => (
                      <Select
                        {...field}
                        options={availableRejectedReasonCodes}
                        placeholder="Select reason"
                        className="w-full"
                      />
                    )}
                  />
                  {errors.reasonCode && (
                    <p className="mt-1 text-sm text-red-600">
                      {errors.reasonCode.message}
                    </p>
                  )}
                </div>
              )}
            </>
          )}

          {/* Rescind Offer Form */}
          {actionType === 'offer.rescinded' && (
            <>
              <div>
                <label className="mb-2 block text-sm font-medium">
                  Reason code
                </label>
                <Controller
                  name="reasonCode"
                  control={control}
                  rules={{ required: 'Please select a reason' }}
                  render={({ field }) => (
                    <Select
                      {...field}
                      options={availableRejectedReasonCodes}
                      placeholder="Select reason"
                      className="w-full"
                    />
                  )}
                />
                {errors.reasonCode && (
                  <p className="mt-1 text-sm text-red-600">
                    {errors.reasonCode.message}
                  </p>
                )}
              </div>

              <div>
                <label className="mb-2 block text-sm font-medium">Notes</label>
                <Controller
                  name="notes"
                  control={control}
                  render={({ field }) => (
                    <Textarea
                      {...field}
                      placeholder="Add context for this decision"
                      rows={3}
                      className="w-full"
                    />
                  )}
                />
              </div>
            </>
          )}

          {/* Close Form */}
          {(selectedStatus === ApplicationStatus.CLOSED ||
            actionType === 'status.closed') && (
            <>
              <div>
                <label className="mb-2 block text-sm font-medium">
                  Reason code
                </label>
                <Controller
                  name="reasonCode"
                  control={control}
                  rules={{ required: 'Please select a reason' }}
                  render={({ field }) => (
                    <Select
                      {...field}
                      options={availableRejectedReasonCodes}
                      placeholder="Select reason"
                      className="w-full"
                    />
                  )}
                />
                {errors.reasonCode && (
                  <p className="mt-1 text-sm text-red-600">
                    {errors.reasonCode.message}
                  </p>
                )}
              </div>

              <div>
                <label className="mb-2 block text-sm font-medium">
                  Notes (optional)
                </label>
                <Controller
                  name="notes"
                  control={control}
                  render={({ field }) => (
                    <Textarea
                      {...field}
                      placeholder="Add context for this decision"
                      rows={3}
                      className="w-full"
                    />
                  )}
                />
              </div>
            </>
          )}

          <div className="flex justify-end gap-3 pt-4">
            <Button onClick={onClose} variant="outline">
              Cancel
            </Button>
            <Button type="submit" variant="solid" className="text-white">
              {actionType ? getActionButtonText(actionType) : 'Save'}
            </Button>
          </div>
        </form>
      </div>
    </Modal>
  );
}

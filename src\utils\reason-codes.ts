interface ReasonCode {
  code: string;
  label: string;
  bucket: string;
  visibility: string;
}

export const reasonCodes = [
  {
    code: 'missing_must_have_skill',
    label: 'Missing must-have skill',
    bucket: 'qualification',
    visibility: 'public',
  },
  {
    code: 'insufficient_experience',
    label: 'Insufficient experience',
    bucket: 'qualification',
    visibility: 'public',
  },
  {
    code: 'overqualified',
    label: 'Overqualified',
    bucket: 'qualification',
    visibility: 'public',
  },
  {
    code: 'education_requirement_not_met',
    label: 'Education requirement not met',
    bucket: 'qualification',
    visibility: 'public',
  },
  {
    code: 'language_requirement_not_met',
    label: 'Language requirement not met',
    bucket: 'qualification',
    visibility: 'public',
  },
  {
    code: 'certification_missing',
    label: 'Missing certification',
    bucket: 'qualification',
    visibility: 'public',
  },

  {
    code: 'failed_screening_questions',
    label: 'Failed screening questions',
    bucket: 'assessment',
    visibility: 'public',
  },
  {
    code: 'failed_assessment_test',
    label: 'Failed assessment/simulation',
    bucket: 'assessment',
    visibility: 'public',
  },
  {
    code: 'failed_technical_interview',
    label: 'Failed technical interview',
    bucket: 'assessment',
    visibility: 'public',
  },
  {
    code: 'failed_behavioral_interview',
    label: 'Failed behavioral interview',
    bucket: 'assessment',
    visibility: 'public',
  },
  {
    code: 'duplicate_application',
    label: 'Duplicate application',
    bucket: 'process',
    visibility: 'public',
  },

  {
    code: 'role_filled_by_other_candidate',
    label: 'Role filled by other candidate',
    bucket: 'business',
    visibility: 'public',
  },
  {
    code: 'salary_expectation_mismatch',
    label: 'Salary expectation mismatch',
    bucket: 'business',
    visibility: 'public',
  },
  {
    code: 'work_authorization_ineligible',
    label: 'Work authorization ineligible',
    bucket: 'compliance',
    visibility: 'public',
  },
  {
    code: 'relocation_unwilling_unavailable',
    label: 'Relocation unwilling/unavailable',
    bucket: 'logistics',
    visibility: 'public',
  },
  {
    code: 'availability_timing_mismatch',
    label: 'Availability timing mismatch',
    bucket: 'logistics',
    visibility: 'public',
  },

  {
    code: 'values_alignment_concerns',
    label: 'Values alignment concerns',
    bucket: 'culture',
    visibility: 'internal',
  },
  {
    code: 'communication_fit_concerns',
    label: 'Communication fit concerns',
    bucket: 'culture',
    visibility: 'internal',
  },

  {
    code: 'background_check_failed',
    label: 'Background check failed',
    bucket: 'compliance',
    visibility: 'internal',
  },
  {
    code: 'reference_check_failed',
    label: 'Reference check failed',
    bucket: 'compliance',
    visibility: 'internal',
  },
  {
    code: 'policy_restriction',
    label: 'Policy restriction',
    bucket: 'compliance',
    visibility: 'internal',
  },

  {
    code: 'headcount_freeze',
    label: 'Headcount freeze',
    bucket: 'org_change',
    visibility: 'public',
  },
  {
    code: 'role_canceled',
    label: 'Role canceled',
    bucket: 'org_change',
    visibility: 'public',
  },
  {
    code: 'budget_constraint',
    label: 'Budget constraint',
    bucket: 'business',
    visibility: 'internal',
  },

  {
    code: 'accepted_other_offer',
    label: 'Accepted other offer',
    bucket: 'candidate_choice',
    visibility: 'public',
  },
  {
    code: 'role_interest_changed',
    label: 'Role interest changed',
    bucket: 'candidate_choice',
    visibility: 'public',
  },
  {
    code: 'timeline_too_long',
    label: 'Timeline too long',
    bucket: 'candidate_choice',
    visibility: 'public',
  },
  {
    code: 'personal_reasons',
    label: 'Personal reasons',
    bucket: 'candidate_choice',
    visibility: 'public',
  },
  {
    code: 'counter_offer_accepted',
    label: 'Counter-offer accepted',
    bucket: 'candidate_choice',
    visibility: 'public',
  },
  {
    code: 'location_policy_mismatch',
    label: 'Location/remote policy mismatch',
    bucket: 'candidate_choice',
    visibility: 'public',
  },
  {
    code: 'unable_to_attend_interviews',
    label: 'Unable to attend interviews',
    bucket: 'logistics',
    visibility: 'public',
  },
  {
    code: 'poor_process_experience',
    label: 'Poor process experience',
    bucket: 'process',
    visibility: 'internal',
  },
  {
    code: 'unresponsive_after_submission',
    label: 'Unresponsive after submission',
    bucket: 'process',
    visibility: 'internal',
  },

  {
    code: 'schedule_conflict',
    label: 'Schedule conflict',
    bucket: 'logistics',
    visibility: 'public',
  },
  {
    code: 'role_misalignment',
    label: 'Role misalignment',
    bucket: 'qualification',
    visibility: 'public',
  },
  {
    code: 'compensation_concern',
    label: 'Compensation concern',
    bucket: 'business',
    visibility: 'public',
  },
  {
    code: 'company_fit_concern',
    label: 'Company/team fit concern',
    bucket: 'culture',
    visibility: 'public',
  },

  {
    code: 'candidate_no_show',
    label: 'Candidate no-show',
    bucket: 'process',
    visibility: 'public',
  },
  {
    code: 'interviewer_no_show',
    label: 'Interviewer no-show',
    bucket: 'process',
    visibility: 'internal',
  },
  {
    code: 'technical_connectivity_issue',
    label: 'Technical connectivity issue',
    bucket: 'logistics',
    visibility: 'public',
  },

  {
    code: 'no_response_from_candidate',
    label: 'No response from candidate',
    bucket: 'process',
    visibility: 'public',
  },
  {
    code: 'notice_short_timeline',
    label: 'Short response timeline',
    bucket: 'process',
    visibility: 'internal',
  },

  {
    code: 'health_reason',
    label: 'Health reason',
    bucket: 'logistics',
    visibility: 'public',
  },
  {
    code: 'travel_issue',
    label: 'Travel issue',
    bucket: 'logistics',
    visibility: 'public',
  },
  {
    code: 'timezone_issue',
    label: 'Timezone issue',
    bucket: 'logistics',
    visibility: 'public',
  },
  {
    code: 'urgent_personal_matter',
    label: 'Urgent personal matter',
    bucket: 'candidate_choice',
    visibility: 'public',
  },

  {
    code: 'did_not_meet_technical_bar',
    label: 'Did not meet technical bar',
    bucket: 'assessment',
    visibility: 'public',
  },
  {
    code: 'did_not_meet_behavioral_bar',
    label: 'Did not meet behavioral bar',
    bucket: 'assessment',
    visibility: 'public',
  },
  {
    code: 'mixed_feedback_needs_stronger_signals',
    label: 'Mixed feedback; needs stronger signals',
    bucket: 'assessment',
    visibility: 'internal',
  },

  {
    code: 'total_compensation_mismatch',
    label: 'Total compensation mismatch',
    bucket: 'business',
    visibility: 'public',
  },
  {
    code: 'benefits_package_inadequate',
    label: 'Benefits package inadequate',
    bucket: 'business',
    visibility: 'public',
  },
  {
    code: 'role_scope_mismatch',
    label: 'Role scope mismatch',
    bucket: 'qualification',
    visibility: 'public',
  },
  {
    code: 'seniority_title_concern',
    label: 'Seniority/title concern',
    bucket: 'qualification',
    visibility: 'public',
  },
  {
    code: 'start_date_timing_issue',
    label: 'Start date timing issue',
    bucket: 'logistics',
    visibility: 'public',
  },
  {
    code: 'location_remote_policy_mismatch',
    label: 'Location/remote policy mismatch',
    bucket: 'logistics',
    visibility: 'public',
  },
  {
    code: 'accepted_competing_offer',
    label: 'Accepted competing offer',
    bucket: 'candidate_choice',
    visibility: 'public',
  },
  {
    code: 'counter_offer_from_current_employer',
    label: 'Counter-offer from current employer',
    bucket: 'candidate_choice',
    visibility: 'public',
  },
  {
    code: 'manager_team_fit_concern',
    label: 'Manager/team fit concern',
    bucket: 'culture',
    visibility: 'public',
  },

  {
    code: 'budget_cut',
    label: 'Budget cut',
    bucket: 'business',
    visibility: 'internal',
  },
  {
    code: 'compliance_issue',
    label: 'Compliance issue',
    bucket: 'compliance',
    visibility: 'internal',
  },
  {
    code: 'offer_error_correction',
    label: 'Offer error correction',
    bucket: 'process',
    visibility: 'internal',
  },

  {
    code: 'job_filled',
    label: 'Job filled',
    bucket: 'business',
    visibility: 'public',
  },
  {
    code: 'job_closed',
    label: 'Job closed',
    bucket: 'business',
    visibility: 'public',
  },
  {
    code: 'hiring_paused',
    label: 'Hiring paused',
    bucket: 'org_change',
    visibility: 'public',
  },
  {
    code: 'org_restructure',
    label: 'Org restructure',
    bucket: 'org_change',
    visibility: 'internal',
  },
  {
    code: 'posting_expired',
    label: 'Posting expired',
    bucket: 'process',
    visibility: 'public',
  },
  {
    code: 'sla_window_expired',
    label: 'SLA window expired',
    bucket: 'system',
    visibility: 'internal',
  },

  {
    code: 'simulation_score_below_threshold',
    label: 'Simulation score below threshold',
    bucket: 'assessment',
    visibility: 'public',
  },
  {
    code: 'task_requirements_not_met',
    label: 'Task requirements not met',
    bucket: 'assessment',
    visibility: 'public',
  },
  {
    code: 'plagiarism_detected',
    label: 'Plagiarism detected',
    bucket: 'compliance',
    visibility: 'internal',
  },
  {
    code: 'insufficient_submission_detail',
    label: 'Insufficient submission detail',
    bucket: 'assessment',
    visibility: 'public',
  },
  {
    code: 'late_submission',
    label: 'Late submission',
    bucket: 'process',
    visibility: 'public',
  },

  {
    code: 'mistaken_status_change',
    label: 'Mistaken status change',
    bucket: 'process',
    visibility: 'internal',
  },
  {
    code: 'data_entry_error',
    label: 'Data entry error',
    bucket: 'process',
    visibility: 'internal',
  },
  {
    code: 'candidate_reconsidered',
    label: 'Candidate reconsidered',
    bucket: 'business',
    visibility: 'internal',
  },
  {
    code: 'approval_override',
    label: 'Approval override',
    bucket: 'process',
    visibility: 'internal',
  },
];

export const reasonCodeMap = [
  {
    code: 'missing_must_have_skill',
    statuses: ['rejected'],
    actions: ['status.rejected'],
  },
  {
    code: 'insufficient_experience',
    statuses: ['rejected'],
    actions: ['status.rejected'],
  },
  {
    code: 'overqualified',
    statuses: ['rejected'],
    actions: ['status.rejected'],
  },
  {
    code: 'education_requirement_not_met',
    statuses: ['rejected'],
    actions: ['status.rejected'],
  },
  {
    code: 'language_requirement_not_met',
    statuses: ['rejected'],
    actions: ['status.rejected'],
  },
  {
    code: 'certification_missing',
    statuses: ['rejected'],
    actions: ['status.rejected'],
  },

  {
    code: 'failed_screening_questions',
    statuses: ['rejected'],
    actions: ['status.rejected'],
  },
  {
    code: 'failed_assessment_test',
    statuses: ['rejected'],
    actions: ['status.rejected', 'assessment.simulation_failed'],
  },
  {
    code: 'failed_technical_interview',
    statuses: ['rejected', 'interview'],
    actions: ['status.rejected', 'interview.completed'],
  },
  {
    code: 'failed_behavioral_interview',
    statuses: ['rejected', 'interview'],
    actions: ['status.rejected', 'interview.completed'],
  },
  {
    code: 'duplicate_application',
    statuses: ['rejected'],
    actions: ['status.rejected'],
  },

  {
    code: 'role_filled_by_other_candidate',
    statuses: ['rejected', 'closed'],
    actions: ['status.rejected', 'status.closed'],
  },
  {
    code: 'salary_expectation_mismatch',
    statuses: ['rejected'],
    actions: ['status.rejected'],
  },
  {
    code: 'work_authorization_ineligible',
    statuses: ['rejected', 'offer'],
    actions: ['status.rejected', 'offer.rescinded'],
  },
  {
    code: 'relocation_unwilling_unavailable',
    statuses: ['rejected'],
    actions: ['status.rejected'],
  },
  {
    code: 'availability_timing_mismatch',
    statuses: ['rejected'],
    actions: ['status.rejected'],
  },

  {
    code: 'values_alignment_concerns',
    statuses: ['rejected'],
    actions: ['status.rejected'],
  },
  {
    code: 'communication_fit_concerns',
    statuses: ['rejected'],
    actions: ['status.rejected'],
  },

  {
    code: 'background_check_failed',
    statuses: ['rejected', 'offer'],
    actions: ['status.rejected', 'offer.rescinded'],
  },
  {
    code: 'reference_check_failed',
    statuses: ['rejected', 'offer'],
    actions: ['status.rejected', 'offer.rescinded'],
  },
  {
    code: 'policy_restriction',
    statuses: ['rejected', 'offer'],
    actions: ['status.rejected', 'offer.rescinded'],
  },

  {
    code: 'headcount_freeze',
    statuses: ['rejected', 'offer', 'closed'],
    actions: ['status.rejected', 'offer.rescinded', 'status.closed'],
  },
  {
    code: 'role_canceled',
    statuses: ['rejected', 'offer', 'closed'],
    actions: ['status.rejected', 'offer.rescinded', 'status.closed'],
  },
  {
    code: 'budget_constraint',
    statuses: ['rejected', 'closed'],
    actions: ['status.rejected', 'status.closed'],
  },

  {
    code: 'accepted_other_offer',
    statuses: ['withdrawn', 'interview', 'offer'],
    actions: ['status.withdrawn', 'interview.declined', 'offer.declined'],
  },
  {
    code: 'role_interest_changed',
    statuses: ['withdrawn'],
    actions: ['status.withdrawn'],
  },
  {
    code: 'timeline_too_long',
    statuses: ['withdrawn'],
    actions: ['status.withdrawn'],
  },
  {
    code: 'personal_reasons',
    statuses: ['withdrawn'],
    actions: ['status.withdrawn'],
  },
  {
    code: 'counter_offer_accepted',
    statuses: ['withdrawn', 'offer'],
    actions: ['status.withdrawn', 'offer.declined'],
  },
  {
    code: 'location_policy_mismatch',
    statuses: ['withdrawn', 'offer'],
    actions: ['status.withdrawn', 'offer.declined'],
  },
  {
    code: 'unable_to_attend_interviews',
    statuses: ['withdrawn', 'interview'],
    actions: ['status.withdrawn', 'interview.declined'],
  },
  {
    code: 'poor_process_experience',
    statuses: ['withdrawn'],
    actions: ['status.withdrawn'],
  },
  {
    code: 'unresponsive_after_submission',
    statuses: ['withdrawn'],
    actions: ['status.withdrawn'],
  },

  {
    code: 'schedule_conflict',
    statuses: ['interview'],
    actions: ['interview.declined'],
  },
  {
    code: 'role_misalignment',
    statuses: ['interview'],
    actions: ['interview.declined'],
  },
  {
    code: 'compensation_concern',
    statuses: ['interview'],
    actions: ['interview.declined'],
  },
  {
    code: 'company_fit_concern',
    statuses: ['interview'],
    actions: ['interview.declined'],
  },

  {
    code: 'candidate_no_show',
    statuses: ['interview'],
    actions: ['interview.no_show'],
  },
  {
    code: 'interviewer_no_show',
    statuses: ['interview'],
    actions: ['interview.no_show'],
  },
  {
    code: 'technical_connectivity_issue',
    statuses: ['interview'],
    actions: ['interview.no_show'],
  },

  {
    code: 'no_response_from_candidate',
    statuses: ['interview', 'offer'],
    actions: ['interview.no_response_expired', 'offer.expired'],
  },
  {
    code: 'notice_short_timeline',
    statuses: ['interview'],
    actions: ['interview.no_response_expired'],
  },

  {
    code: 'health_reason',
    statuses: ['interview'],
    actions: ['interview.reschedule_requested'],
  },
  {
    code: 'travel_issue',
    statuses: ['interview'],
    actions: ['interview.reschedule_requested'],
  },
  {
    code: 'timezone_issue',
    statuses: ['interview'],
    actions: ['interview.reschedule_requested'],
  },
  {
    code: 'urgent_personal_matter',
    statuses: ['interview'],
    actions: ['interview.reschedule_requested'],
  },

  {
    code: 'did_not_meet_technical_bar',
    statuses: ['interview'],
    actions: ['interview.completed'],
  },
  {
    code: 'did_not_meet_behavioral_bar',
    statuses: ['interview'],
    actions: ['interview.completed'],
  },
  {
    code: 'mixed_feedback_needs_stronger_signals',
    statuses: ['interview'],
    actions: ['interview.completed'],
  },

  {
    code: 'total_compensation_mismatch',
    statuses: ['offer'],
    actions: ['offer.declined'],
  },
  {
    code: 'benefits_package_inadequate',
    statuses: ['offer'],
    actions: ['offer.declined'],
  },
  {
    code: 'role_scope_mismatch',
    statuses: ['offer'],
    actions: ['offer.declined'],
  },
  {
    code: 'seniority_title_concern',
    statuses: ['offer'],
    actions: ['offer.declined'],
  },
  {
    code: 'start_date_timing_issue',
    statuses: ['offer'],
    actions: ['offer.declined'],
  },
  {
    code: 'location_remote_policy_mismatch',
    statuses: ['offer'],
    actions: ['offer.declined'],
  },
  {
    code: 'accepted_competing_offer',
    statuses: ['offer'],
    actions: ['offer.declined'],
  },
  {
    code: 'counter_offer_from_current_employer',
    statuses: ['offer'],
    actions: ['offer.declined'],
  },
  {
    code: 'manager_team_fit_concern',
    statuses: ['offer'],
    actions: ['offer.declined'],
  },

  { code: 'budget_cut', statuses: ['offer'], actions: ['offer.rescinded'] },
  {
    code: 'compliance_issue',
    statuses: ['offer'],
    actions: ['offer.rescinded'],
  },
  {
    code: 'offer_error_correction',
    statuses: ['offer'],
    actions: ['offer.rescinded'],
  },

  { code: 'job_filled', statuses: ['closed'], actions: ['status.closed'] },
  { code: 'job_closed', statuses: ['closed'], actions: ['status.closed'] },
  { code: 'hiring_paused', statuses: ['closed'], actions: ['status.closed'] },
  { code: 'org_restructure', statuses: ['closed'], actions: ['status.closed'] },
  { code: 'posting_expired', statuses: ['closed'], actions: ['status.closed'] },
  {
    code: 'sla_window_expired',
    statuses: ['closed'],
    actions: ['status.closed'],
  },

  {
    code: 'simulation_score_below_threshold',
    statuses: ['rejected', 'under_review'],
    actions: ['status.rejected', 'assessment.simulation_failed'],
  },
  {
    code: 'task_requirements_not_met',
    statuses: ['rejected', 'under_review'],
    actions: ['status.rejected', 'assessment.simulation_failed'],
  },
  {
    code: 'plagiarism_detected',
    statuses: ['rejected', 'under_review', 'offer'],
    actions: [
      'status.rejected',
      'assessment.simulation_failed',
      'offer.rescinded',
    ],
  },
  {
    code: 'insufficient_submission_detail',
    statuses: ['rejected', 'under_review'],
    actions: ['status.rejected', 'assessment.simulation_failed'],
  },
  {
    code: 'late_submission',
    statuses: ['rejected', 'under_review'],
    actions: ['status.rejected', 'assessment.simulation_failed'],
  },

  {
    code: 'mistaken_status_change',
    statuses: ['hired', 'rejected', 'withdrawn'],
    actions: ['status.restore'],
  },
  {
    code: 'data_entry_error',
    statuses: ['hired', 'rejected', 'withdrawn'],
    actions: ['status.restore'],
  },
  {
    code: 'candidate_reconsidered',
    statuses: ['rejected'],
    actions: ['status.restore'],
  },
  {
    code: 'approval_override',
    statuses: ['hired', 'rejected', 'withdrawn'],
    actions: ['status.restore'],
  },
];

export const reasonCodeBuckets = [
  { code: 'qualification', label: 'Qualification' },
  { code: 'assessment', label: 'Assessment' },
  { code: 'process', label: 'Process' },
  { code: 'business', label: 'Business' },
  { code: 'compliance', label: 'Compliance' },
  { code: 'logistics', label: 'Logistics' },
  { code: 'culture', label: 'Culture' },
  { code: 'org_change', label: 'Org change' },
  { code: 'candidate_choice', label: 'Candidate choice' },
  { code: 'system', label: 'System' },
];

export const getStatusReasonCodes = (
  status?: string | null,
  action?: string
) => {
  if (!status && !action) return [];
  const codes = reasonCodeMap
    .filter((code) => {
      return !!status
        ? code.statuses.includes(status) &&
            (!action || code.actions.includes(action))
        : code.actions.includes(action || '');
    })
    .map((code) => code.code);

  const reasonOptions = reasonCodes.filter((code) => codes.includes(code.code));

  return reasonOptions;
};

const mapReasonCodes = reasonCodes.reduce(
  (acc, code) => {
    acc[code.code] = code;
    return acc;
  },
  {} as Record<string, ReasonCode>
);

export const getReasonCodeLabel = (code: string) => {
  return mapReasonCodes[code]?.label || code;
};

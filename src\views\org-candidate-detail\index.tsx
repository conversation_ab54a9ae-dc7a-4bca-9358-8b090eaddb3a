'use client';

import { useGetCandidateByOrg } from '@/api-requests/job-candidate/get-candidate-by-org';
import { ApplicationStatus } from '@/api-requests/job-candidate/types';
import { useUpdateApplicationStatus } from '@/api-requests/job-candidate/update-application-status';
import { SelectOption } from '@/api-requests/types';
import Breadcrumb from '@/shared/breadcrumb';
import { orgAtom } from '@/store/organization-atom';
import { safeFormatDate } from '@/utils/date';
import { format } from 'date-fns';
import { useAtom } from 'jotai';
import {
  Briefcase,
  CalendarClock,
  FileDown,
  Heart,
  Mail,
  MapPin,
  MoreVertical,
} from 'lucide-react';
import { useParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import { createPortal } from 'react-dom';
import {
  ActionIcon,
  Avatar,
  Badge,
  Button,
  Dropdown,
  Loader,
  Select,
  Tooltip,
} from 'rizzui';
import CadidateTabs from './candidate-tabs';
import CannotChangeStatusDialog from './cannot-change-status-dialog';
import ChangeStatusModal, { StatusChangeFormData } from './change-status-modal';
import ConfirmChangeStatusDialog from './confirm-application-status-dialog';

const statusOptions = [
  { label: 'Draft', value: ApplicationStatus.DRAFT },
  { label: 'Submitted', value: ApplicationStatus.SUBMITTED },
  { label: 'Under Review', value: ApplicationStatus.UNDER_REVIEW },
  { label: 'Interview', value: ApplicationStatus.INTERVIEW },
  { label: 'Rejected', value: ApplicationStatus.REJECTED },
  // { label: 'Withdrawn', value: ApplicationStatus.WITHDRAWN },
  { label: 'Offer', value: ApplicationStatus.OFFER },
  { label: 'Hired', value: ApplicationStatus.HIRED },
  { label: 'Closed', value: ApplicationStatus.CLOSED },
  // { label: 'Offer Accepted', value: ApplicationStatus.OFFER_ACCEPTED },
  // { label: 'Offer Declined', value: ApplicationStatus.OFFER_DECLINED },
];

const actions = [
  {
    icon: <Heart className="h-5 w-5" />,
    label: 'Add to Shortlist',
  },
  {
    icon: <Mail className="h-5 w-5" />,
    label: 'Contact',
  },
  {
    icon: <FileDown className="h-5 w-5" />,
    label: 'Export data',
  },
];

export default function CandidateDetail() {
  const { id: applicationId } = useParams<{ id: string }>();
  const [org] = useAtom(orgAtom);
  const { data: candidate, isFetched, refetch: refreshCandidate } = useGetCandidateByOrg(
    org?._id || '',
    applicationId
  );
  const { mutateAsync: updateApplicationStatus, isPending: isUpdatingStatus } =
    useUpdateApplicationStatus();
  const [currentStatus, setCurrentStatus] = useState<SelectOption | null>(null);
  const [newStatus, setNewStatus] = useState<ApplicationStatus | null>(null);
  const [openConfirmStatusDialog, setOpenConfirmStatusDialog] = useState(false);
  const [openCannotChangeDialog, setOpenCannotChangeDialog] = useState(false);
  const [openChangeStatusModal, setOpenChangeStatusModal] = useState(false);

  const handleStatusChange = async (option: SelectOption) => {
    try {
      if (
        isUpdatingStatus ||
        !candidate?.applicationStatus ||
        candidate.applicationStatus === option.value
      )
        return;

      // Check if status cannot be changed
      const nonChangeableStatuses = [
        ApplicationStatus.DRAFT,
        ApplicationStatus.HIRED,
        ApplicationStatus.REJECTED,
        ApplicationStatus.CLOSED,
      ];

      if (nonChangeableStatuses.includes(candidate.applicationStatus)) {
        setNewStatus(option.value as ApplicationStatus);
        setOpenCannotChangeDialog(true);
        return;
      }

      // Open the new change status modal
      setOpenChangeStatusModal(true);
    } catch (error) {}
  };

  const handleConfirmStatusChange = async (value: string) => {
    setOpenConfirmStatusDialog(false);

    try {
      const changedOption =
        statusOptions.find((option) => option.value === value) || null;
      setCurrentStatus(changedOption);

      await updateApplicationStatus({
        applicationId,
        status: value,
        orgId: (org?._id || '') as string,
      });
      if (candidate) candidate.applicationStatus = value as ApplicationStatus;
    } catch (error) {
      setCurrentStatus(
        statusOptions.find(
          (option) => option.value === candidate?.applicationStatus
        ) || null
      );
    } finally {
      setNewStatus(null);
    }
  };

  const handleCloseCannotChangeStatusDialog = () => {
    setOpenCannotChangeDialog(false);
    setNewStatus(null);
  };

  const handleChangeStatusSubmit = async (data: StatusChangeFormData) => {
    try {
      console.log('Status change data:', data);

      // TODO: Must validate the data first. (Maybe use Zod)
      await updateApplicationStatus({
        applicationId,
        orgId: (org?._id || '') as string,

        targetStatus: data.targetStatus?.value,
        reasonCode: data.reasonCode?.value,
        notes: data.notes,
        ...(!!data.scheduledAt
          ? {
              localScheduledDate: format(data.scheduledAt, 'yyyy-MM-dd'),
              localScheduledTime: format(data.scheduledAt, 'HH:mm'),
            }
          : {}),
        timezone: data.timezone?.value,
        ...(!!data.respondDeadline
          ? {
              localResponseDate: format(data.respondDeadline, 'yyyy-MM-dd'),
              localResponseTime: format(data.respondDeadline, 'HH:mm'),
            }
          : {}),
        ...(!!data.startDate
          ? {
              localStartDate: format(data.startDate, 'yyyy-MM-dd'),
              localStartTime: format(data.startDate, 'HH:mm'),
            }
          : {}),
        ...(!!data.offerExpiresAt
          ? {
              localOfferExpiresDate: format(data.offerExpiresAt, 'yyyy-MM-dd'),
              localOfferExpiresTime: format(data.offerExpiresAt, 'HH:mm'),
            }
          : {}),
        offerAmount: data.offerAmount,
        currency: data.currency?.value,
        employmentType: data.employmentType?.value,
        jobTitleLevel: data.jobTitleLevel,
      });

      // Update local state
      if (candidate) {
        candidate.applicationStatus = data.targetStatus
          ?.value as ApplicationStatus;
      }

      setCurrentStatus(
        statusOptions.find(
          (option) => option.value === data.targetStatus?.value
        ) || null
      );

      setOpenChangeStatusModal(false);
    } catch (error) {
      console.error('Error updating status:', error);
    }
  };

  const handleExportPDF = () => {};
  const handleAddToShortList = () => {};
  const handleContact = () => {};
  const handleShare = () => {};

  const handleClickAction = (action: string, candidate: any) => {
    switch (action) {
      case 'Add to Shortlist':
        handleAddToShortList();
        break;
      default:
        break;
    }
  };

  useEffect(() => {
    if (isFetched) {
      setCurrentStatus(
        statusOptions.find(
          (option) => option.value === candidate?.applicationStatus
        ) || null
      );
    }
  }, [isFetched]);

  if (!isFetched)
    return (
      <div className="flex h-full items-center justify-center">
        <Loader />
      </div>
    );

  if (!candidate)
    return (
      <div className="flex h-full flex-col items-center justify-center gap-5">
        <p>Oops! An error occurred while fetching candidate data.</p>
        <Button
          variant="solid"
          className="text-white"
          onClick={() => window.location.reload()}
        >
          Reload
        </Button>
      </div>
    );

  return (
    <>
      <div className="space-y-6">
        <Breadcrumb
          items={[
            { name: 'Candidate List', href: '/org/admin/candidates' },
            {
              name:
                candidate.user?.firstName && candidate.user?.lastName
                  ? `${candidate.user?.firstName} ${candidate.user?.lastName}`
                  : 'Candidate Details',
              href: `/org/admin/candidates/${applicationId}`,
            },
          ]}
        />

        {/* Header Section */}
        <div className="bg-background/80 supports-[backdrop-filter]:bg-background/60 sticky top-0 z-40 border-b border-[#c3c3c3] p-6 backdrop-blur">
          <div className="flex flex-col gap-6 lg:flex-row lg:items-start lg:justify-between">
            {/* Left Section - Candidate Info */}
            <div className="flex flex-col gap-6 sm:flex-row sm:items-center">
              {/* Avatar */}
              <div className="relative shrink-0">
                <Avatar
                  src={candidate.user?.avatar || '/avatar/user-default.png'}
                  name={candidate.user?.firstName || ''}
                  customSize={100}
                  className="!bg-transparent"
                />
              </div>

              {/* Candidate Details */}
              <div className="flex-1 space-y-3">
                <div className="flex flex-col gap-2 sm:flex-row sm:items-center sm:gap-4">
                  <h1 className="text-2xl font-bold text-primary">
                    {candidate.user?.firstName} {candidate.user?.lastName}
                  </h1>
                  <div className="flex items-center gap-2">
                    <Badge variant="solid" size="md" className="text-white">
                      {candidate.matchPercentage ?? '--'}% match
                    </Badge>
                    <Badge variant="solid" size="md" className="text-white">
                      Applied with{' '}
                      {candidate.applyMode === 'cv' ? 'CV' : 'Simulation'}
                    </Badge>
                  </div>
                </div>

                {/* Job and Application Info */}
                <div className="space-y-1 text-sm text-primary">
                  <div className="flex items-center gap-2">
                    <Badge
                      variant="outline"
                      size="md"
                      className="flex flex-row gap-1"
                    >
                      <Briefcase className="h-4 w-4" />
                      <span>
                        {candidate.job?.title ||
                          candidate.simulation?.name ||
                          '-'}
                      </span>
                    </Badge>
                    <span>•</span>
                    <Tooltip
                      color="invert"
                      content={safeFormatDate(candidate.appliedAt, {
                        format: 'full',
                      })}
                    >
                      <Badge
                        variant="outline"
                        size="md"
                        className="flex flex-row gap-1"
                      >
                        <CalendarClock className="h-4 w-4" />
                        <span>
                          {safeFormatDate(candidate.appliedAt, {
                            format: 'relative',
                          })}
                        </span>
                      </Badge>
                    </Tooltip>
                  </div>
                </div>

                {(!!candidate.job?.city || !!candidate.job?.country) && (
                  <div className="flex flex-wrap items-center gap-4 text-sm">
                    <Badge
                      variant="outline"
                      size="md"
                      className="flex flex-row gap-1 text-primary"
                    >
                      <MapPin className="h-4 w-4" />
                      <span>
                        {[candidate.job?.city, candidate.job?.country]
                          .filter(Boolean)
                          .join(', ')}
                      </span>
                    </Badge>
                  </div>
                )}
              </div>
            </div>

            {/* Right Section - Status and Actions */}
            <div className="flex flex-col gap-4 sm:flex-row sm:items-center lg:flex-col lg:items-end">
              {/* Status Dropdown */}
              {/* <div className="flex items-center gap-2">
              <Select
                value={displayStatus}
                onChange={handleStatusChange}
                options={statusOptions}
                className="min-w-[140px]"
                placeholder="Select status"
              />
            </div> */}

              {/* Action Buttons */}
              <div className="flex items-center gap-2">
                <Select
                  value={currentStatus}
                  onChange={handleStatusChange}
                  options={statusOptions}
                  className="min-w-[140px]"
                  placeholder="Select status"
                  prefix="Status:"
                  disabled={
                    isUpdatingStatus ||
                    !candidate?.applicationStatus ||
                    [
                      ApplicationStatus.DRAFT,
                      ApplicationStatus.HIRED,
                      ApplicationStatus.REJECTED,
                      ApplicationStatus.CLOSED,
                    ].includes(candidate?.applicationStatus)
                  }
                />

                <Dropdown placement="bottom-end">
                  {/* @ts-ignore */}
                  <Dropdown.Trigger as="div">
                    <ActionIcon variant="outline" className="rounded-xl">
                      <MoreVertical className="h-4 w-4" />
                    </ActionIcon>
                  </Dropdown.Trigger>
                  <Dropdown.Menu className="w-fit divide-y">
                    {actions.map((action, idx) => (
                      <Dropdown.Item
                        key={idx}
                        className="hover:bg-primary hover:text-white"
                        onClick={() =>
                          handleClickAction(action.label, candidate)
                        }
                      >
                        <div className="flex items-center">
                          {action.icon}
                          <span className="ml-2">{action.label}</span>
                        </div>
                      </Dropdown.Item>
                    ))}
                  </Dropdown.Menu>
                </Dropdown>
              </div>
            </div>
          </div>
        </div>

        {/* Tabs Section */}
        <CadidateTabs candidate={candidate} refreshCandidate={refreshCandidate}
        />
      </div>

      {openCannotChangeDialog &&
        !!candidate?.applicationStatus &&
        !!newStatus &&
        createPortal(
          <CannotChangeStatusDialog
            open={openCannotChangeDialog}
            setOpen={handleCloseCannotChangeStatusDialog}
            currentStatus={
              statusOptions.find(
                (option) => option.value === candidate.applicationStatus
              )?.label || ''
            }
            newStatus={
              statusOptions.find((option) => option.value === newStatus)
                ?.label || ''
            }
          />,
          document.body
        )}

      {openConfirmStatusDialog &&
        !!candidate?.applicationStatus &&
        !!newStatus &&
        createPortal(
          <ConfirmChangeStatusDialog
            open={openConfirmStatusDialog}
            setOpen={setOpenConfirmStatusDialog}
            currentStatus={
              statusOptions.find(
                (option) => option.value === candidate.applicationStatus
              )?.label || ''
            }
            newStatus={
              statusOptions.find((option) => option.value === newStatus)
                ?.label || ''
            }
            onConfirm={() => handleConfirmStatusChange(newStatus)}
          />,
          document.body
        )}

      {openChangeStatusModal &&
        !!candidate?.applicationStatus &&
        createPortal(
          <ChangeStatusModal
            open={openChangeStatusModal}
            onClose={() => setOpenChangeStatusModal(false)}
            currentStatus={candidate.applicationStatus}
            candidateName={`${candidate.user?.firstName || ''} ${candidate.user?.lastName || ''}`.trim()}
            jobTitle={
              candidate.job?.title || candidate.simulation?.name || 'Position'
            }
            onSubmit={handleChangeStatusSubmit}
          />,
          document.body
        )}
    </>
  );
}

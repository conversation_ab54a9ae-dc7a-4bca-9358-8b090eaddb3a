'use client';

import {
  ApplicationStatus,
  JobCandidate,
} from '@/api-requests/job-candidate/types';
import { useUpdateApplicationStatus } from '@/api-requests/job-candidate/update-application-status';
import useApplicationStatus from '@/hooks/use-application-status';
import { Section } from '@/shared/section';
import { convertApplicationStatus } from '@/utils/application-simulation-status';
import cn from '@/utils/class-names';
import { safeFormatDate } from '@/utils/date';
import { getReasonCodeLabel } from '@/utils/reason-codes';
import { format } from 'date-fns';
import {
  AlertCircle,
  Calendar,
  CheckCircle,
  DollarSign,
  Edit,
  FileText,
  Loader2,
  MoreVertical,
  RotateCcw,
  UserCheck,
  UserX,
  X,
  XCircle,
} from 'lucide-react';
import { useState } from 'react';
import { Badge, Button, Dropdown } from 'rizzui';
import ChangeStatusModal, {
  StatusChangeFormData,
} from '../change-status-modal';

interface StatusActionsSectionProps {
  candidate: JobCandidate;
  onRefresh: () => void;
}

interface ActionButton {
  label: string;
  icon: React.ReactNode;
  variant: 'primary' | 'secondary' | 'danger';
  action: () => void;
}

export default function StatusActionsSection({
  candidate,
  onRefresh,
}: StatusActionsSectionProps) {
  const { getApplicationStatusClassName } = useApplicationStatus();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedAction, setSelectedAction] = useState<string>('');
  const [targetStatus, setTargetStatus] = useState<ApplicationStatus | null>(
    null
  );

  const { mutateAsync: updateStatusMutateAsync, isPending: isUpdatingStatus } =
    useUpdateApplicationStatus();

  const currentStatus = candidate.applicationStatus;
  const currentApplicationId = candidate._id;
  const currentInterview = candidate.interviewStat || undefined;
  const currentOffer = candidate.offerStat || undefined;

  const handleActionClick = (action: string, status?: ApplicationStatus) => {
    setSelectedAction(action);

    // Set target status based on action
    let targetStatusForAction: ApplicationStatus | null = null;
    switch (action) {
      case 'move_to_under_review':
        targetStatusForAction = ApplicationStatus.UNDER_REVIEW;
        break;
      case 'interview.scheduled':
        targetStatusForAction = ApplicationStatus.INTERVIEW;
        break;
      case 'create_offer':
        targetStatusForAction = ApplicationStatus.OFFER;
        break;
      case 'mark_hired':
        targetStatusForAction = ApplicationStatus.HIRED;
        break;
      case 'status.rejected':
        targetStatusForAction = ApplicationStatus.REJECTED;
        break;
      case 'status.closed':
        targetStatusForAction = ApplicationStatus.CLOSED;
        break;
      default:
        targetStatusForAction = status || null;
    }

    setTargetStatus(targetStatusForAction);
    setIsModalOpen(true);
  };

  const handleModalSubmit = async (data: StatusChangeFormData) => {
    try {
      const payload: any = {
        applicationId: currentApplicationId,
        orgId: candidate.orgId!,
      };

      switch (selectedAction) {
        case 'move_to_under_review':
          payload.targetStatus = ApplicationStatus.UNDER_REVIEW;
          break;
        case 'interview.scheduled':
        case 'interview.rescheduled':
          payload.targetStatus = ApplicationStatus.INTERVIEW;
          payload.action = selectedAction;
          if (data.scheduledAt) {
            payload.localScheduledDate = format(data.scheduledAt, 'yyyy-MM-dd');
            payload.localScheduledTime = format(data.scheduledAt, 'HH:mm');
          }
          if (data.respondDeadline) {
            payload.localResponseDate = format(
              data.respondDeadline,
              'yyyy-MM-dd'
            );
            payload.localResponseTime = format(data.respondDeadline, 'HH:mm');
          }
          payload.timezone = data.timezone?.value;
          payload.notes = data.notes;
          break;
        case 'interview.completed':
          payload.targetStatus = ApplicationStatus.INTERVIEW;
          payload.action = selectedAction;
          payload.notes = data.notes;
          if (!!data.reasonCode?.value)
            payload.reasonCode = data.reasonCode.value;
          break;
        case 'interview.canceled_by_interviewer':
          payload.targetStatus = ApplicationStatus.INTERVIEW;
          payload.action = selectedAction;
          payload.notes = data.notes;
          break;
        case 'create_offer':
        case 'update_offer':
          payload.targetStatus = ApplicationStatus.OFFER;
          payload.action = selectedAction;
          payload.jobTitleLevel = data.jobTitleLevel;
          if (data.startDate) {
            payload.localStartDate = format(data.startDate, 'yyyy-MM-dd');
            payload.localStartTime = format(data.startDate, 'HH:mm');
          }
          payload.offerExpiresAt = data.offerExpiresAt;
          if (payload.offerExpiresAt) {
            payload.localOfferExpiresDate = format(
              payload.offerExpiresAt,
              'yyyy-MM-dd'
            );
            payload.localOfferExpiresTime = format(
              payload.offerExpiresAt,
              'HH:mm'
            );
          }
          payload.timezone = data.timezone?.value;
          payload.offerAmount = data.offerAmount;
          payload.currency = data.currency?.value;
          break;
        case 'offer.accepted':
          payload.targetStatus = ApplicationStatus.OFFER;
          payload.action = selectedAction;
          payload.notes = data.notes;
          break;
        case 'offer.declined':
        case 'offer.rescinded':
          payload.targetStatus = ApplicationStatus.OFFER;
          payload.action = selectedAction;
          payload.reasonCode = data.reasonCode?.value;
          payload.notes = data.notes;
          break;
        case 'mark_hired':
          payload.targetStatus = ApplicationStatus.HIRED;
          payload.action = selectedAction;
          if (data.startDate) {
            payload.localStartDate = format(data.startDate, 'yyyy-MM-dd');
            payload.localStartTime = format(data.startDate, 'HH:mm');
          }
          // payload.startDate = data.startDate;
          payload.employmentType = data.employmentType?.value;
          payload.timezone = data.timezone?.value;
          break;
        case 'status.rejected':
          payload.action = selectedAction;
          payload.targetStatus = ApplicationStatus.REJECTED;
          payload.reasonCode = data.reasonCode?.value;
          payload.notes = data.notes;
          break;
        case 'status.closed':
          payload.action = selectedAction;
          payload.targetStatus = ApplicationStatus.CLOSED;
          payload.reasonCode = data.reasonCode?.value;
          payload.notes = data.notes;
          break;
        default:
          break;
      }

      await updateStatusMutateAsync(payload);

      // Show success message based on action
      // let successMessage = 'Status updated successfully';
      // switch (selectedAction) {
      //   case 'interview.scheduled':
      //     successMessage = 'Interview scheduled successfully';
      //     break;
      //   case 'create_offer':
      //     successMessage = 'Offer created successfully';
      //     break;
      //   case 'mark_hired':
      //     successMessage = 'Candidate marked as hired';
      //     break;
      //   case 'status.rejected':
      //     successMessage = 'Application rejected';
      //     break;
      //   case 'status.closed':
      //     successMessage = 'Application closed';
      //     break;
      // }

      // if (selectedAction) toast.success(successMessage);
      setIsModalOpen(false);
      onRefresh();
    } catch (error: any) {
      console.error('Error updating status:', error);
      // toast.error(
      //   error?.response?.data?.message ||
      //     error?.message ||
      //     'Failed to update status'
      // );
    }
  };

  const getActionsForStatus = (status: ApplicationStatus): ActionButton[] => {
    const actions: ActionButton[] = [];

    switch (status) {
      case ApplicationStatus.DRAFT:
        // No actions for draft
        break;

      case ApplicationStatus.SUBMITTED:
        actions.push(
          {
            label: 'Move to Under Review',
            icon: <FileText className="h-4 w-4" />,
            variant: 'primary',
            action: () => handleActionClick('move_to_under_review'),
          },
          {
            label: 'Reject',
            icon: <UserX className="h-4 w-4" />,
            variant: 'danger',
            action: () => handleActionClick('status.rejected'),
          },
          {
            label: 'Close',
            icon: <X className="h-4 w-4" />,
            variant: 'secondary',
            action: () => handleActionClick('status.closed'),
          }
        );
        break;

      case ApplicationStatus.UNDER_REVIEW:
        actions.push(
          {
            label: 'Schedule Interview',
            icon: <Calendar className="h-4 w-4" />,
            variant: 'primary',
            action: () => handleActionClick('interview.scheduled'),
          },
          {
            label: 'Reject',
            icon: <UserX className="h-4 w-4" />,
            variant: 'danger',
            action: () => handleActionClick('status.rejected'),
          },
          {
            label: 'Close',
            icon: <X className="h-4 w-4" />,
            variant: 'secondary',
            action: () => handleActionClick('status.closed'),
          }
        );
        break;

      case ApplicationStatus.INTERVIEW:
        if (currentInterview) {
          const interviewStatus = currentInterview.status;

          if (
            interviewStatus === 'scheduled' ||
            interviewStatus === 'accepted'
          ) {
            actions.push(
              {
                label: 'Reschedule',
                icon: <RotateCcw className="h-4 w-4" />,
                variant: 'secondary',
                action: () => handleActionClick('interview.rescheduled'),
              },
              {
                label: 'Mark as Completed',
                icon: <CheckCircle className="h-4 w-4" />,
                variant: 'primary',
                action: () => handleActionClick('interview.completed'),
              },
              {
                label: 'Cancel',
                icon: <X className="h-4 w-4" />,
                variant: 'secondary',
                action: () =>
                  handleActionClick('interview.canceled_by_interviewer'),
              }
            );
          }

          if (interviewStatus === 'completed') {
            actions.push(
              {
                label: 'Create Offer',
                icon: <DollarSign className="h-4 w-4" />,
                variant: 'primary',
                action: () => handleActionClick('create_offer'),
              },
              {
                label: 'Reject',
                icon: <UserX className="h-4 w-4" />,
                variant: 'danger',
                action: () => handleActionClick('status.rejected'),
              }
            );
          }
        }

        actions.push({
          label: 'Close',
          icon: <X className="h-4 w-4" />,
          variant: 'secondary',
          action: () => handleActionClick('status.closed'),
        });
        break;

      case ApplicationStatus.OFFER:
        if (currentOffer) {
          const offerStatus = currentOffer.status;

          actions.push({
            label: 'Update Offer',
            icon: <Edit className="h-4 w-4" />,
            variant: 'secondary',
            action: () => handleActionClick('update_offer'),
          });

          if (offerStatus === 'pending') {
            actions.push(
              {
                label: 'Mark as Accepted',
                icon: <CheckCircle className="h-4 w-4" />,
                variant: 'primary',
                action: () => handleActionClick('offer.accepted'),
              },
              {
                label: 'Mark as Declined',
                icon: <XCircle className="h-4 w-4" />,
                variant: 'secondary',
                action: () => handleActionClick('offer.declined'),
              }
            );
          }

          if (offerStatus === 'accepted') {
            actions.push({
              label: 'Mark as Hired',
              icon: <UserCheck className="h-4 w-4" />,
              variant: 'primary',
              action: () => handleActionClick('mark_hired'),
            });
          }

          if (['pending', 'accepted'].includes(offerStatus)) {
            actions.push({
              label: 'Rescind',
              icon: <AlertCircle className="h-4 w-4" />,
              variant: 'danger',
              action: () => handleActionClick('offer.rescinded'),
            });
          }

          actions.push({
            label: 'Close',
            icon: <X className="h-4 w-4" />,
            variant: 'secondary',
            action: () => handleActionClick('status.closed'),
          });
        }
        break;

      case ApplicationStatus.REJECTED:
      case ApplicationStatus.WITHDRAWN:
      case ApplicationStatus.CLOSED:
      case ApplicationStatus.HIRED:
        // No actions for final states
        break;
    }

    return actions;
  };

  const getStatusInfo = () => {
    let statusText = convertApplicationStatus(currentStatus);
    let subStatusText = '';

    if (currentStatus === ApplicationStatus.INTERVIEW && currentInterview) {
      const interviewStatus = currentInterview.status;
      switch (interviewStatus) {
        case 'scheduled':
          subStatusText = 'Scheduled';
          break;
        case 'accepted':
          subStatusText = 'Accepted by candidate';
          break;
        case 'declined':
          subStatusText = 'Declined by candidate';
          break;
        case 'completed':
          subStatusText = 'Completed';
          break;
        case 'canceled_by_interviewer':
          subStatusText = 'Canceled by interviewer';
          break;
        case 'canceled_by_candidate':
          subStatusText = 'Canceled by candidate';
          break;
        case 'no_show':
          subStatusText = 'No show';
          break;
        case 'no_response':
          subStatusText = 'No response';
          break;
      }
    }

    if (currentStatus === ApplicationStatus.OFFER && currentOffer) {
      const offerStatus = currentOffer.status;
      switch (offerStatus) {
        case 'pending':
          subStatusText = 'Pending response';
          break;
        case 'accepted':
          subStatusText = 'Accepted';
          break;
        case 'declined':
          subStatusText = 'Declined';
          break;
        case 'expired':
          subStatusText = 'Expired';
          break;
        case 'rescinded':
          subStatusText = 'Rescinded';
          break;
      }
    }

    return { statusText, subStatusText };
  };

  const actions = getActionsForStatus(currentStatus);
  const { statusText, subStatusText } = getStatusInfo();

  if (!currentStatus) return null;

  return (
    <>
      <Section>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold">Current Status & Actions</h3>
          </div>

          <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
            <div className="flex flex-col gap-2">
              <div className="flex flex-col gap-2 sm:flex-row sm:items-center sm:gap-3">
                <Badge
                  variant="flat"
                  size="md"
                  className={cn(getApplicationStatusClassName(currentStatus))}
                >
                  {statusText}
                </Badge>
                {subStatusText && (
                  <span className="text-sm text-gray-600 sm:ml-0">
                    <span className="hidden sm:inline">• </span>
                    {subStatusText}
                  </span>
                )}
              </div>
              {/* Show additional info for interview/offer */}
              {currentInterview &&
                currentStatus === ApplicationStatus.INTERVIEW && (
                  <div>
                    <div className="text-xs text-gray-500">
                      Scheduled:{' '}
                      {safeFormatDate(currentInterview.scheduledAt, {
                        format: 'full',
                      })}
                      {/* {new Date(
                      currentInterview.scheduledAt
                    ).toLocaleDateString()} */}
                    </div>
                    <div className="text-xs text-gray-500">
                      Notes: {currentInterview?.notes}
                    </div>
                  </div>
                )}
              {currentOffer && currentStatus === ApplicationStatus.OFFER && (
                <div className="text-xs text-gray-500">
                  Amount: {currentOffer.salary.currency}{' '}
                  {currentOffer.salary.amount.toLocaleString()}
                </div>
              )}
              {currentStatus === ApplicationStatus.REJECTED && (
                <div>
                  <div className="text-xs text-gray-500">
                    Reason:{' '}
                    {getReasonCodeLabel(
                      candidate.rejectedStat?.reasonCode || ''
                    )}
                  </div>
                  <div className="text-xs text-gray-500">
                    Notes: {candidate.rejectedStat?.notes}
                  </div>
                </div>
              )}
              {currentStatus === ApplicationStatus.CLOSED && (
                <div>
                  <div className="text-xs text-gray-500">
                    Reason:{' '}
                    {getReasonCodeLabel(candidate.closedStat?.reasonCode || '')}
                  </div>
                  <div className="text-xs text-gray-500">
                    Notes: {candidate.closedStat?.notes}
                  </div>
                </div>
              )}
            </div>

            {actions.length > 0 && (
              <div className="flex flex-wrap gap-2">
                {actions.slice(0, 3).map((action, index) => (
                  <Button
                    key={index}
                    size="sm"
                    variant={action.variant === 'primary' ? 'solid' : 'outline'}
                    className={cn(
                      action.variant === 'primary' && 'text-white',
                      action.variant === 'danger' &&
                        'border-red-500 text-red-600 hover:bg-red-50'
                    )}
                    onClick={action.action}
                    disabled={isUpdatingStatus}
                  >
                    {isUpdatingStatus ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      action.icon
                    )}
                    <span className="ml-2 hidden sm:inline">
                      {action.label}
                    </span>
                    <span className="ml-2 sm:hidden">
                      {action.label.split(' ')[0]}
                    </span>
                  </Button>
                ))}
                {actions.length > 3 && (
                  <Dropdown placement="bottom-end">
                    {/* @ts-ignore */}
                    <Dropdown.Trigger as="div">
                      <Button size="sm" variant="outline">
                        <MoreVertical className="h-4 w-4" />
                      </Button>
                    </Dropdown.Trigger>
                    <Dropdown.Menu className="w-fit divide-y">
                      {actions.slice(3).map((action, idx) => (
                        <Dropdown.Item
                          key={idx}
                          className="hover:bg-primary hover:text-white"
                          onClick={() => {
                            action.action?.();
                          }}
                        >
                          <div className="flex items-center">
                            {action.icon}
                            <span className="ml-2">{action.label}</span>
                          </div>
                        </Dropdown.Item>
                      ))}
                    </Dropdown.Menu>
                  </Dropdown>
                )}
              </div>
            )}
          </div>
        </div>
      </Section>

      <ChangeStatusModal
        open={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        currentStatus={currentStatus}
        candidateName={candidate.user?.firstName || candidate.email}
        jobTitle={
          candidate.job?.title || candidate.simulation?.name || 'Position'
        }
        onSubmit={handleModalSubmit}
        actionType={selectedAction}
        targetStatus={targetStatus || undefined}
      />
    </>
  );
}

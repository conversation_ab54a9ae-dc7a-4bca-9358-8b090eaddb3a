'use client';

import { JobCandidate } from '@/api-requests/job-candidate/types';
import { useState } from 'react';
import { Tab } from 'rizzui';
import AllPositionsTab from './all-positions-tab';
import OverviewTab from './overview-tab';
import ProfileTab from './profile-tab';

interface IProps {
  candidate: JobCandidate;
  refreshCandidate: () => void;
}

const tabs = [
  { name: 'Overview', id: 0 },
  { name: 'Profile', id: 1 },
  { name: 'All Positions', id: 2 },
];

export default function CadidateTabs({ candidate, refreshCandidate }: IProps) {
  const [activeTab, setActiveTab] = useState(0);
  return (
    <Tab
      className={'[&_.rizzui-tab-list]:border-none'}
      selectedIndex={activeTab}
      onChange={(idx) => setActiveTab(idx)}
    >
      <Tab.List>
        {tabs.map((tab) => (
          <Tab.ListItem key={tab.id}>{tab.name}</Tab.ListItem>
        ))}
        {/* <Tab.ListItem>Overview</Tab.ListItem>
        <Tab.ListItem>Profile</Tab.ListItem>
        <Tab.ListItem>All Positions</Tab.ListItem> */}
      </Tab.List>
      <Tab.Panels>
        <Tab.Panel>
          <OverviewTab
            candidate={candidate}
            onOpenAllPositions={() => setActiveTab(2)}
          />
        </Tab.Panel>
        <Tab.Panel>
          <ProfileTab candidate={candidate} />
        </Tab.Panel>
        <Tab.Panel>
          <AllPositionsTab
            candidate={candidate}
            refreshCandidate={refreshCandidate}
          />
        </Tab.Panel>
      </Tab.Panels>
    </Tab>
  );
}

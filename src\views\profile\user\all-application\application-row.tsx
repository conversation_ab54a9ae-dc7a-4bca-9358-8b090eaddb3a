'use client';

import { JobStatus } from '@/api-requests/job';
import { ApplicationStatus } from '@/api-requests/job-candidate/types';
import { UserApplication } from '@/api-requests/user-profile';
import { useUpdateMyApplicationStatus } from '@/api-requests/user-profile/update-my-application-status';
import { useWithdrawApplication } from '@/api-requests/user-profile/withdraw-application';
import StartSimulationButton from '@/components/StartSimulationButton';
import useApplicationStatus from '@/hooks/use-application-status';
import useSimulationStatus from '@/hooks/use-simulation-status';
import { Image } from '@/shared/image';
import { convertSimulationStatus } from '@/utils/application-simulation-status';
import { safeFormatDate } from '@/utils/date';
import { getApplyModeText } from '@/views/candidate-list/candidates-table';
import { AlertTriangle, PlayCircle } from 'lucide-react';
import Link from 'next/link';
import { useState } from 'react';
import toast from 'react-hot-toast';
import { <PERSON>ge, Button, Tooltip } from 'rizzui';
import {
  getActionModalConfig,
  getAvailableActions,
} from './application-actions-utils';
import { getApplicationStatusInfo } from './application-status-utils';
import ReasonCodeModal from './reason-code-modal';

const getLocation = (application: UserApplication) => {
  if (application.job?.city || application.job?.country) {
    return `${application.job.city ? `${application.job.city}, ` : ''}${application.job.country}`;
  }
  if (application.org?.city || application.org?.country) {
    return `${application.org.city ? `${application.org.city}, ` : ''}${application.org.country}`;
  }
  return '';
};

interface IProps {
  application: UserApplication;
  onOpenDetail: (id: string) => void;
  onRefresh: () => void;
}

export default function ApplicationRow({
  application,
  onOpenDetail,
  onRefresh,
}: IProps) {
  const { getApplicationStatusClassName } = useApplicationStatus();
  const { getSimulationStatusClassName } = useSimulationStatus();

  // State for modal
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedAction, setSelectedAction] = useState<string>('');

  // API hooks
  const { mutateAsync: updateApplicationStatus, isPending: isUpdatingStatus } =
    useUpdateMyApplicationStatus();
  const { mutateAsync: withdrawApplication, isPending: isWithdrawing } =
    useWithdrawApplication();

  // Get status and action information
  const statusInfo = getApplicationStatusInfo(application);
  const availableActions = getAvailableActions(application);

  const hasNextStep =
    application.simulationStatus && application.simulationStatus === 'active';
  const shouldContinueSimulation =
    (application.applicationStatus === ApplicationStatus.DRAFT &&
      application.job?.status &&
      application.job?.status !== JobStatus.CLOSED) ||
    (application.simulation?.status &&
      application.simulation?.status === 'published');

  const isLoading = isUpdatingStatus || isWithdrawing;

  // Handle action clicks
  const handleActionClick = (action: string) => {
    setSelectedAction(action);
    setIsModalOpen(true);
  };

  // Handle modal confirm
  const handleModalConfirm = async (data: {
    reasonCode?: string;
    notes?: string;
  }) => {
    try {
      if (selectedAction === 'status.withdrawn') {
        await withdrawApplication({
          id: application._id,
          reasonCode: data.reasonCode,
          notes: data.notes,
        });
        toast.success('Application withdrawn successfully.');
      } else {
        await updateApplicationStatus({
          applicationId: application._id,
          targetStatus: application.applicationStatus,
          action: selectedAction as any,
          reasonCode: data.reasonCode,
          notes: data.notes,
        });
        toast.success('Application updated successfully.');
      }

      setIsModalOpen(false);
      setSelectedAction('');
      onRefresh();
    } catch (error) {
      toast.error('Failed to update application. Please try again.');
    }
  };

  const handleModalClose = () => {
    setIsModalOpen(false);
    setSelectedAction('');
  };

  return (
    <div className="rounded-2xl border border-[#c3c3c3] p-4">
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        {/* Left section */}
        <div className="flex min-w-0 items-start gap-4 md:max-w-[50%]">
          {application?.org?.logo || application?.job?.companyLogoUrl ? (
            <Image
              src={
                application?.org?.logo || application?.job?.companyLogoUrl || ''
              }
              fallbackSrc="/org/default-logo.png"
              alt={application.org?.name || application.job?.companyName}
              width={60}
              height={60}
              className="h-15 w-15 object-contain"
              loader={({ src }) => src}
            />
          ) : (
            <div
              className="!h-15 !w-15 rounded-full bg-gray-100"
              style={{ width: '60px', height: '60px' }}
            />
          )}
          <div className="min-w-0">
            <div className="flex flex-wrap items-center gap-2">
              <p
                className="text-left text-base font-medium hover:underline"
                onClick={() => onOpenDetail(application._id)}
              >
                {application.job?.title || application.simulation.name}
              </p>
              {/* <Badge variant="outline" size="sm">
                Closed
              </Badge> */}
              {(application.job && application.job?.status === 'closed') ||
                (application.simulation &&
                  application.simulation?.status !== 'published' && (
                    <Badge variant="outline" size="sm">
                      Closed
                    </Badge>
                  ))}
            </div>
            <div className="text-sm text-gray-600">
              <p className="truncate">
                {application.org?._id ? (
                  <Link
                    href={`/org/${application.org?._id}`}
                    target="_blank"
                    className="hover:underline"
                  >
                    <span>
                      {application.org?.name || application.job?.companyName}
                    </span>
                  </Link>
                ) : (
                  <span>
                    {application.org?.name || application.job?.companyName}
                  </span>
                )}
              </p>
              <p className="truncate">{getLocation(application)}</p>
            </div>
            <div className="mt-2 flex flex-wrap gap-1.5">
              <Badge variant="outline" size="sm">
                {getApplyModeText(application.applyMode)}
              </Badge>
              {application.applyMode === 'cv' &&
                !!application.quickQuestions?.length && (
                  <Badge variant="outline" size="sm">
                    Questions
                  </Badge>
                )}
              {application.applyMode === 'simulation' &&
                !!application.simulationStatus && (
                  <Badge
                    variant="flat"
                    size="sm"
                    className={getSimulationStatusClassName(
                      application.simulationStatus as 'active' | 'completed'
                    )}
                  >
                    {convertSimulationStatus(application.simulationStatus)}
                  </Badge>
                )}
            </div>
          </div>
        </div>

        {/* Right meta */}
        <div className="grid grid-cols-3 items-center gap-6 md:text-right">
          <div className="text-sm">
            <div className="font-bold">Applied</div>
            <div>
              <Tooltip
                content={safeFormatDate(application.appliedAt, {
                  format: 'full',
                })}
                size="sm"
                color="invert"
              >
                <span className="text-sm">
                  {safeFormatDate(application.appliedAt, {
                    format: 'long',
                  })}
                </span>
              </Tooltip>
              {/* {safeFormatDate(application.appliedAt, { format: 'short' })} */}
            </div>
          </div>

          <div className="text-center md:text-right">
            <div className="text-sm font-bold">Match</div>
            {application.matchPercentage !== undefined ? (
              <span className="font-bold">{application.matchPercentage}%</span>
            ) : (
              <Tooltip
                color="invert"
                content="You've not completed the simulation"
              >
                <span>--%</span>
              </Tooltip>
            )}
          </div>

          <div className="flex flex-col items-end gap-1">
            <div className="flex items-center gap-2">
              <Badge
                variant="flat"
                size="sm"
                className={getApplicationStatusClassName(
                  application.applicationStatus
                )}
              >
                {statusInfo.statusText}
              </Badge>
            </div>
            {statusInfo.subStatusText && (
              <span className="text-xs text-gray-500">
                {statusInfo.subStatusText}
              </span>
            )}
            {statusInfo.deadlineInfo && (
              <div className="flex items-center gap-1 text-xs">
                {statusInfo.deadlineInfo.isExpired && (
                  <AlertTriangle className="h-3 w-3 text-red-500" />
                )}
                <span
                  className={
                    statusInfo.deadlineInfo.isExpired
                      ? 'font-medium text-red-600'
                      : statusInfo.deadlineInfo.isUrgent
                        ? 'font-medium text-orange-600'
                        : 'text-gray-500'
                  }
                >
                  {statusInfo.deadlineInfo.text}
                </span>
              </div>
            )}
          </div>
        </div>
      </div>

      <hr className="my-4 border border-t-[#c3c3c3]" />

      {/* Actions */}
      <div className="flex flex-wrap items-center justify-between gap-3">
        <div className="text-sm text-gray-500">
          {application.job?.status === JobStatus.CLOSED ||
          (application.simulation &&
            application.simulation?.status !== 'published') ? (
            <span>This job is closed to new actions.</span>
          ) : hasNextStep ? (
            <span>Continue to complete the application.</span>
          ) : availableActions.length > 0 ? (
            <span>Actions available for this application.</span>
          ) : (
            <>No pending actions.</>
          )}
        </div>

        <div className="flex flex-wrap items-center gap-2">
          {shouldContinueSimulation && (
            <StartSimulationButton
              jobId={application.jobId || ''}
              simId={application.simulationId || ''}
              buttonProps={{ className: 'text-white gap-1', size: 'sm' }}
            >
              <PlayCircle className="h-4 w-4" /> Continue
            </StartSimulationButton>
          )}

          {/* Action buttons */}
          {availableActions.slice(0, 2).map((action) => (
            <Button
              key={action.id}
              variant={action.variant === 'primary' ? 'solid' : 'outline'}
              size="sm"
              className={
                action.variant === 'primary'
                  ? 'text-white'
                  : action.variant === 'danger'
                    ? 'border-red-500 text-red-500 hover:border-red-700 hover:text-red-700'
                    : ''
              }
              onClick={() => handleActionClick(action.action)}
              disabled={isLoading}
            >
              {action.label}
            </Button>
          ))}

          {/* More actions dropdown if needed */}
          {availableActions.length > 2 && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                // For now, just show the third action
                if (availableActions[2]) {
                  handleActionClick(availableActions[2].action);
                }
              }}
              disabled={isLoading}
            >
              More
            </Button>
          )}
        </div>
      </div>

      {/* Reason Code Modal */}
      {isModalOpen && selectedAction && (
        <ReasonCodeModal
          open={isModalOpen}
          onClose={handleModalClose}
          onConfirm={handleModalConfirm}
          isLoading={isLoading}
          title={
            getActionModalConfig(
              selectedAction,
              application.job?.title ||
                application.simulation?.name ||
                'Position'
            ).title
          }
          description={
            getActionModalConfig(
              selectedAction,
              application.job?.title ||
                application.simulation?.name ||
                'Position'
            ).description
          }
          action={selectedAction}
          status={application.applicationStatus}
          requireReasonCode={
            getActionModalConfig(
              selectedAction,
              application.job?.title ||
                application.simulation?.name ||
                'Position'
            ).requireReasonCode
          }
        />
      )}
    </div>
  );
}

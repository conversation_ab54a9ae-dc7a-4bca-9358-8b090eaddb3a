'use client';

import { Job } from '@/api-requests/job';
import { userAtom } from '@/store/user-atom';
import cn from '@/utils/class-names';
import { capitalizeFirst } from '@/utils/string';
import ClockIcon from '@/views/icons/clock';
import LocationIcon from '@/views/icons/location';
import { getJobTypeString } from '@/views/job/job-list';
import { useAtom } from 'jotai';
import { useRouter } from 'next/navigation';

const getJobLocation = (job: Job) => {
  let jobLocation = job?.location || '';
  if (!!job?.city || !!job?.country) {
    jobLocation = `${job.city}${!!job.city ? ', ' : ''}${job.country}`;
  }
  return jobLocation;
};
interface JobCardProps {
  job: Job;
}

export default function JobCard({ job }: JobCardProps) {
  const router = useRouter();
  const [user] = useAtom(userAtom);

  const jobType = getJobTypeString(job);

  const handleApplyNow = () => {
    if (!job?.jobId) return;

    router.push(`/find-jobs?id=${job.jobId}`);
    // window.open(
    //   `${API_DOMAINS.AGENTOS_CLOUD}/job-simulation/${job.simulation?.id}?trail=true`,
    //   '_blank'
    // );
  };

  return (
    <div
      className={cn(
        'flex h-full flex-col justify-between rounded-[16px] bg-white p-6 transition-all duration-300 hover:scale-[1.01] hover:shadow-[0_4px_30px_rgba(0,0,0,0.20)]'
      )}
    >
      <div className="flex h-full flex-col gap-4">
        <div className="flex items-center justify-between gap-2">
          <div className="flex flex-1 items-center gap-2">
            <div className="h-12 w-12 overflow-hidden">
              <img
                src={job.companyLogoUrl || '/org/default-logo.png'}
                alt={job.companyName || 'Company Logo'}
                // width={32}
                // height={32}
                className="h-full object-contain"
                // loader={({ src }) => src}
                onError={(e) => {
                  e.currentTarget.src = '/org/default-logo.png';
                }}
              />
            </div>
            <div className="line-clamp-1">{job?.companyName}</div>
          </div>
          {job?.simulation?.id && (
            <div className="flex items-center gap-1 rounded-full border border-[#05F98F] bg-[#EDFFF1] px-3 py-1 text-sm hover:bg-[#CCFFE7]">
              {/* <StarsIcon className="h-4 w-4 text-[#A36EFF]" /> */}
              <span className="text-[#00BF50]">Proof ready</span>
            </div>
          )}
        </div>

        {/* Title + Badge */}
        <div>
          <div className="text-md line-clamp-2 flex-1 font-semibold text-gray-800">
            {job?.title}
          </div>
          <div className="flex items-center gap-2">
            <div className="flex items-center gap-1 text-[12px] text-gray-500">
              <LocationIcon className="h-3 w-3" />
              <span>{getJobLocation(job)}</span>
            </div>
            <div className="flex items-center gap-1 text-[12px] text-gray-500">
              <ClockIcon className="h-3 w-3" />
              <span>{jobType || '-'}</span>
            </div>
          </div>
        </div>

        <div>
          {/* 3 columns */}
          {/* <div className="grid grid-cols-1 gap-4 md:grid-cols-2"> */}
          {/* Estimated time */}
          <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
            <div>
              <div className="text-sm text-gray-500">Mini-task (min)</div>
              <div className="mt-1 font-bold">
                {job?.simulation?.minute || '--'}
              </div>
            </div>

            {/* Past outcomes */}
            <div>
              <div className="text-sm text-gray-500">With proof</div>
              <div className="mt-1 font-bold">58%</div>
            </div>

            <div>
              <div className="text-sm text-gray-500">Expected fit</div>
              <div className="mt-1 font-bold">
                {job?.expectedFit || !user?.id ? (
                  `${job?.expectedFit || '--'}%`
                ) : (
                  <button
                    className="text-sm font-medium italic underline"
                    onClick={(e) => {
                      e.stopPropagation();
                      router.push(`profile/${user.id}#profile`);
                    }}
                  >
                    Upload CV to calculate
                  </button>
                )}
              </div>
            </div>
          </div>

          {/* Predicted score + progress */}
          {/* <div className="min-w-0">
              <div className="text-sm text-slate-400">Predicted Score</div>
              <div className="mt-1 flex items-center gap-4">
                <div className="shrink-0 font-bold">
                  {72}&ndash;{84}%
                </div>
                <div className="relative h-2 w-full rounded-full bg-slate-200">
                  <div
                    className="absolute inset-y-0 left-0 rounded-full bg-gradient-to-r from-indigo-500 via-fuchsia-500 to-cyan-400"
                    style={{ width: `${84}%` }}
                  />
                </div>
              </div>
            </div> */}
          {/* </div> */}

          {/* Tags */}
          <div className="mt-4 flex min-h-7 flex-wrap items-center gap-3 overflow-hidden">
            {job?.skills?.map((t) => (
              <span
                key={t}
                className="bg-[#748CAB]/9 inline-flex items-center rounded-full border border-[#c3c3c3] px-2 py-1 text-[12px] text-[#748CAB]"
              >
                {capitalizeFirst(t)}
              </span>
            ))}
          </div>
        </div>

        <div className="mt-auto flex items-center justify-between">
          <button
            className="rounded-full bg-primary px-4 py-2 text-sm font-medium text-white hover:bg-primary/80"
            onClick={handleApplyNow}
          >
            Apply now
          </button>
          <button
            className="ml-4 text-sm font-medium text-gray-600 hover:underline"
            onClick={(e) => {
              e.stopPropagation();
              router.push(`/find-jobs?id=${job.jobId}`);
            }}
          >
            View details
          </button>
        </div>
      </div>
    </div>
  );
}

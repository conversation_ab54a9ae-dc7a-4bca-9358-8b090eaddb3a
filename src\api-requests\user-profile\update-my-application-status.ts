import { API_ENDPONTS } from '@/config/endpoint';
import axiosInstance from '@/utils/http-client';
import { useMutation, UseMutationOptions } from '@tanstack/react-query';
import { AxiosError } from 'axios';
import { ApplicationStatus } from '../job-candidate/types';

export interface UpdateMyApplicationStatusParams {
  applicationId: string;
  targetStatus: ApplicationStatus;
  action:
    | 'interview.accepted'
    | 'interview.declined'
    | 'interview.canceled_by_candidate'
    | 'offer.accepted'
    | 'offer.declined'
    | 'status.withdrawn';
  reasonCode?: string;
  notes?: string;
}

export async function updateMyApplicationStatus(
  params: UpdateMyApplicationStatusParams
): Promise<boolean> {
  const { applicationId, ...restData } = params;
  const response = await axiosInstance.patch(
    API_ENDPONTS.UPDATE_MY_APPLICATION_STATUS.replace(':id', applicationId),
    restData
  );
  return response.data.success;
}

export const useUpdateMyApplicationStatus = (
  options?: UseMutationOptions<any, AxiosError, UpdateMyApplicationStatusParams>
) => {
  return useMutation({
    mutationFn: (params: UpdateMyApplicationStatusParams) =>
      updateMyApplicationStatus(params),
    ...options,
  });
};

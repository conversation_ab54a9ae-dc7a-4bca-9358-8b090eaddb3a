import { ApplicationStatus } from '@/api-requests/job-candidate/types';
import { UserApplication } from '@/api-requests/user-profile/types';
import { safeFormatDate } from '@/utils/date';

export interface StatusInfo {
  statusText: string;
  subStatusText?: string;
  deadlineInfo?: {
    text: string;
    isExpired: boolean;
    isUrgent: boolean; // within 24 hours
  };
}

export function getApplicationStatusInfo(application: UserApplication): StatusInfo {
  const { applicationStatus, interviewStat, offerStat } = application;
  
  let statusText = '';
  let subStatusText = '';
  let deadlineInfo: StatusInfo['deadlineInfo'];

  // Map status to display text
  switch (applicationStatus) {
    case ApplicationStatus.DRAFT:
      statusText = 'Draft';
      break;
    case ApplicationStatus.SUBMITTED:
      statusText = 'Submitted';
      break;
    case ApplicationStatus.UNDER_REVIEW:
      statusText = 'Under Review';
      break;
    case ApplicationStatus.INTERVIEW:
      statusText = 'Interview';
      break;
    case ApplicationStatus.OFFER:
      statusText = 'Offer';
      break;
    case ApplicationStatus.REJECTED:
      statusText = 'Rejected';
      break;
    case ApplicationStatus.WITHDRAWN:
      statusText = 'Withdrawn';
      break;
    case ApplicationStatus.HIRED:
      statusText = 'Hired';
      break;
    case ApplicationStatus.CLOSED:
      statusText = 'Closed';
      break;
    default:
      statusText = 'Unknown';
  }

  // Add sub-status for Interview
  if (applicationStatus === ApplicationStatus.INTERVIEW && interviewStat) {
    switch (interviewStat.status) {
      case 'scheduled':
        subStatusText = 'Scheduled';
        break;
      case 'accepted':
        subStatusText = 'Accepted';
        break;
      case 'declined':
        subStatusText = 'Declined';
        break;
      case 'completed':
        subStatusText = 'Completed';
        break;
      case 'canceled_by_interviewer':
        subStatusText = 'Canceled by interviewer';
        break;
      case 'canceled_by_candidate':
        subStatusText = 'Canceled by you';
        break;
      case 'no_show':
        subStatusText = 'No show';
        break;
      case 'no_response':
        subStatusText = 'No response';
        break;
    }

    // Check for response deadline
    if (interviewStat.responseDeadline && 
        ['scheduled'].includes(interviewStat.status)) {
      const deadline = new Date(interviewStat.responseDeadline);
      const now = new Date();
      const isExpired = deadline < now;
      const hoursUntilDeadline = (deadline.getTime() - now.getTime()) / (1000 * 60 * 60);
      const isUrgent = hoursUntilDeadline <= 24 && hoursUntilDeadline > 0;

      deadlineInfo = {
        text: `Respond by ${safeFormatDate(interviewStat.responseDeadline, { format: 'full' })}`,
        isExpired,
        isUrgent,
      };
    }
  }

  // Add sub-status for Offer
  if (applicationStatus === ApplicationStatus.OFFER && offerStat) {
    switch (offerStat.status) {
      case 'pending':
        subStatusText = 'Pending response';
        break;
      case 'accepted':
        subStatusText = 'Accepted';
        break;
      case 'declined':
        subStatusText = 'Declined';
        break;
      case 'expired':
        subStatusText = 'Expired';
        break;
      case 'rescinded':
        subStatusText = 'Rescinded';
        break;
    }

    // Check for offer expiration
    if (offerStat.offerExpiresAt && 
        ['pending'].includes(offerStat.status)) {
      const deadline = new Date(offerStat.offerExpiresAt);
      const now = new Date();
      const isExpired = deadline < now;
      const hoursUntilDeadline = (deadline.getTime() - now.getTime()) / (1000 * 60 * 60);
      const isUrgent = hoursUntilDeadline <= 24 && hoursUntilDeadline > 0;

      deadlineInfo = {
        text: `Offer expires ${safeFormatDate(offerStat.offerExpiresAt, { format: 'full' })}`,
        isExpired,
        isUrgent,
      };
    }
  }

  return {
    statusText,
    subStatusText,
    deadlineInfo,
  };
}

import { JobStatus } from '@/api-requests/job';
import { ApplicationStatus } from '@/api-requests/job-candidate/types';
import { UserApplication } from '@/api-requests/user-profile/types';

export interface ActionButton {
  id: string;
  label: string;
  variant: 'primary' | 'secondary' | 'danger';
  action: string;
  requiresReasonCode?: boolean;
}

export function getAvailableActions(application: UserApplication): ActionButton[] {
  const { applicationStatus, interviewStat, offerStat, job, simulation } = application;
  const actions: ActionButton[] = [];

  // Check if job is closed
  const isJobClosed = 
    (job && job.status === JobStatus.CLOSED) ||
    (simulation && simulation.status !== 'published');

  if (isJobClosed) {
    return []; // No actions available for closed jobs
  }

  switch (applicationStatus) {
    case ApplicationStatus.DRAFT:
      // Continue simulation action is handled separately in the main component
      break;

    case ApplicationStatus.SUBMITTED:
      actions.push({
        id: 'withdraw',
        label: 'Withdraw',
        variant: 'danger',
        action: 'status.withdrawn',
        requiresReasonCode: true,
      });
      break;

    case ApplicationStatus.UNDER_REVIEW:
      actions.push({
        id: 'withdraw',
        label: 'Withdraw',
        variant: 'danger',
        action: 'status.withdrawn',
        requiresReasonCode: true,
      });
      break;

    case ApplicationStatus.INTERVIEW:
      if (interviewStat) {
        const interviewStatus = interviewStat.status;

        if (interviewStatus === 'scheduled') {
          actions.push(
            {
              id: 'accept-interview',
              label: 'Accept',
              variant: 'primary',
              action: 'interview.accepted',
            },
            {
              id: 'decline-interview',
              label: 'Decline',
              variant: 'secondary',
              action: 'interview.declined',
              requiresReasonCode: true,
            }
          );
        }

        if (interviewStatus === 'accepted') {
          actions.push({
            id: 'cancel-interview',
            label: 'Cancel',
            variant: 'danger',
            action: 'interview.canceled_by_candidate',
          });
        }

        // Allow withdraw after decline, cancel, or expired
        if (['declined', 'canceled_by_candidate', 'no_response'].includes(interviewStatus)) {
          actions.push({
            id: 'withdraw',
            label: 'Withdraw',
            variant: 'danger',
            action: 'status.withdrawn',
            requiresReasonCode: true,
          });
        }
      }
      break;

    case ApplicationStatus.OFFER:
      if (offerStat) {
        const offerStatus = offerStat.status;

        if (offerStatus === 'pending') {
          actions.push(
            {
              id: 'accept-offer',
              label: 'Accept Offer',
              variant: 'primary',
              action: 'offer.accepted',
            },
            {
              id: 'decline-offer',
              label: 'Decline Offer',
              variant: 'secondary',
              action: 'offer.declined',
              requiresReasonCode: true,
            }
          );
        }

        // Allow withdraw after decline or expired
        if (['declined', 'expired'].includes(offerStatus)) {
          actions.push({
            id: 'withdraw',
            label: 'Withdraw',
            variant: 'danger',
            action: 'status.withdrawn',
            requiresReasonCode: true,
          });
        }
      }
      break;

    case ApplicationStatus.REJECTED:
    case ApplicationStatus.WITHDRAWN:
    case ApplicationStatus.CLOSED:
    case ApplicationStatus.HIRED:
      // No actions available for final states
      break;
  }

  return actions;
}

export function getActionModalConfig(action: string, jobTitle: string) {
  switch (action) {
    case 'interview.accepted':
      return {
        title: 'Accept Interview',
        description: `Accept the interview invitation for ${jobTitle}?`,
        requireReasonCode: false,
      };
    case 'interview.declined':
      return {
        title: 'Decline Interview',
        description: `Decline the interview invitation for ${jobTitle}. Please select a reason.`,
        requireReasonCode: true,
      };
    case 'interview.canceled_by_candidate':
      return {
        title: 'Cancel Interview',
        description: `Cancel your accepted interview for ${jobTitle}?`,
        requireReasonCode: false,
      };
    case 'offer.accepted':
      return {
        title: 'Accept Offer',
        description: `Accept the job offer for ${jobTitle}?`,
        requireReasonCode: false,
      };
    case 'offer.declined':
      return {
        title: 'Decline Offer',
        description: `Decline the job offer for ${jobTitle}. Please select a reason.`,
        requireReasonCode: true,
      };
    case 'status.withdrawn':
      return {
        title: 'Withdraw Application',
        description: `Withdraw your application for ${jobTitle}. Please select a reason.`,
        requireReasonCode: true,
      };
    default:
      return {
        title: 'Confirm Action',
        description: `Are you sure you want to proceed with this action for ${jobTitle}?`,
        requireReasonCode: false,
      };
  }
}

import { useGetCandidateAllPositionsByOrg } from '@/api-requests/job-candidate/get-candidate-all-positions-by-org';
import { useSetOrgPrimaryCandidatePosition } from '@/api-requests/job-candidate/set-org-primary-candidate-position';
import {
  ApplicationStatus,
  JobCandidate,
} from '@/api-requests/job-candidate/types';
import { Section } from '@/shared/section';
import { convertApplicationStatus } from '@/utils/application-simulation-status';
import { safeFormatDate } from '@/utils/date';
import AllPositions from './all-positions';
import StatusActionsSection from './status-actions-section';

interface IProps {
  candidate: JobCandidate;
  refreshCandidate: () => void;
}

export default function AllPositionsTab({
  candidate,
  refreshCandidate,
}: IProps) {
  const { userId, orgId } = candidate;

  const {
    data: allPositionsData,
    isLoading,
    refetch: refreshAllPositions,
  } = useGetCandidateAllPositionsByOrg({
    candidateId: userId,
    orgId: orgId!,
  });

  const {
    mutateAsync: setPrimaryPositionMutateAsync,
    isPending: isSettingPrimaryPosition,
  } = useSetOrgPrimaryCandidatePosition();

  const handleSetPrimaryPosition = async (applicationId: string) => {
    try {
      await setPrimaryPositionMutateAsync({
        applicationId,
        candidateId: userId,
        orgId: orgId!,
      });
      refreshAllPositions();
      refreshCandidate();
    } catch (error) {}
  };

  const { highestStage, offer, nextInterview } = allPositionsData || {};

  if (!userId || !orgId) return null;
  return (
    <div className="space-y-4">
      {!isLoading && !!allPositionsData && (
        <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
          <Section>
            <div className="mb-2 text-sm text-gray-500">Highest Stage</div>
            <div className="text-md font-bold text-gray-900">
              {convertApplicationStatus(
                (highestStage?.status || '--') as ApplicationStatus
              )}
            </div>
          </Section>
          <Section>
            <div className="mb-2 text-sm text-gray-500">Next Interview</div>
            <div className="text-md font-bold text-gray-900">
              {safeFormatDate(nextInterview?.interviewStat?.scheduledAt, {
                format: 'full',
                fallback: '--',
              }) || '--'}
            </div>
          </Section>
          <Section>
            <div className="mb-2 text-sm text-gray-500">Has Offer</div>
            <div className="text-md font-bold text-gray-900">
              {!!offer ? 'Yes' : 'No'}
            </div>
          </Section>
        </div>
      )}
      {/* Status Actions Section */}
      {!isLoading && !!allPositionsData && (
        <StatusActionsSection
          candidate={candidate}
          onRefresh={() => {
            refreshAllPositions();
            refreshCandidate();
          }}
        />
      )}
      <Section>
        <AllPositions
          allPositionsData={{
            data: allPositionsData?.data || [],
            meta: allPositionsData?.meta || { total: 0, limit: 0, page: 1 },
          }}
          isLoadingPositions={isLoading}
          isSettingPrimaryPosition={isSettingPrimaryPosition}
          onSetPrimaryPosition={handleSetPrimaryPosition}
        />
      </Section>
    </div>
  );
}

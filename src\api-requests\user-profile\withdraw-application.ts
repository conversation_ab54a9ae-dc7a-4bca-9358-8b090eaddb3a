import { API_ENDPONTS } from '@/config/endpoint';
import { requestPost } from '@/utils/http-client';
import { useMutation, UseMutationOptions } from '@tanstack/react-query';
import { AxiosError } from 'axios';

export async function withdrawApplication(params: {
  id: string;
  reasonCode?: string;
  notes?: string;
}): Promise<boolean> {
  const { id, ...restData } = params;
  const response = await requestPost<{ success: boolean }>(
    API_ENDPONTS.WITHDRAW_APPLICATION.replace(':id', id),
    restData,
    {}
  );
  return response.data.success;
}

export const useWithdrawApplication = (
  options?: UseMutationOptions<any, AxiosError, { id: string; reasonCode?: string; notes?: string }>
) => {
  return useMutation({
    mutationFn: (params: { id: string; reasonCode?: string; notes?: string }) => withdrawApplication(params),
    ...options,
  });
};

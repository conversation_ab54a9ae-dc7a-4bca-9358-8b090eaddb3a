'use client';

import { getStatusReasonCodes } from '@/utils/reason-codes';
import { useState } from 'react';
import { Button, Modal, Select, Textarea } from 'rizzui';

interface ReasonCodeModalProps {
  open: boolean;
  onClose: () => void;
  onConfirm: (data: { reasonCode?: string; notes?: string }) => void;
  isLoading: boolean;
  title: string;
  description: string;
  action: string;
  status?: string;
  requireReasonCode?: boolean;
}

export default function ReasonCodeModal({
  open,
  onClose,
  onConfirm,
  isLoading,
  title,
  description,
  action,
  status,
  requireReasonCode = false,
}: ReasonCodeModalProps) {
  const [selectedReasonCode, setSelectedReasonCode] = useState<{
    label: string;
    value: string;
  } | null>(null);
  const [notes, setNotes] = useState('');

  const reasonCodes = getStatusReasonCodes(
    // TODO: define & use targetStatus
    action === 'status.withdrawn' ? 'withdrawn' : status,
    action
  );
  console.log('get reason code ::: ', status, action);
  const reasonCodeOptions = reasonCodes.map((code) => ({
    label: code.label,
    value: code.code,
  }));

  const handleConfirm = () => {
    if (requireReasonCode && !selectedReasonCode) {
      return;
    }

    onConfirm({
      reasonCode: selectedReasonCode?.value,
      notes: notes.trim() || undefined,
    });
  };

  const handleClose = () => {
    setSelectedReasonCode(null);
    setNotes('');
    onClose();
  };

  return (
    <Modal isOpen={open} onClose={handleClose} size="lg">
      <div className="flex flex-col gap-5 p-6">
        <div className="text-left">
          <h3 className="text-xl font-bold text-gray-900">{title}</h3>
          <p className="mt-2 text-sm text-gray-600">{description}</p>
        </div>

        <div className="space-y-4">
          {reasonCodeOptions.length > 0 && (
            <div>
              <label className="mb-2 block text-sm font-medium text-gray-700">
                Reason{' '}
                {requireReasonCode && <span className="text-red-500">*</span>}
              </label>
              <Select
                options={reasonCodeOptions}
                value={selectedReasonCode}
                onChange={setSelectedReasonCode}
                placeholder="Select a reason"
                clearable={!requireReasonCode}
              />
            </div>
          )}

          <div>
            <label className="mb-2 block text-sm font-medium text-gray-700">
              Additional Notes (Optional)
            </label>
            <Textarea
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              placeholder="Add any additional comments..."
              rows={3}
            />
          </div>
        </div>

        <div className="flex justify-end gap-3">
          <Button variant="outline" onClick={handleClose} disabled={isLoading}>
            Cancel
          </Button>
          <Button
            variant="solid"
            className="text-white"
            onClick={handleConfirm}
            disabled={isLoading || (requireReasonCode && !selectedReasonCode)}
          >
            {isLoading ? 'Processing...' : 'Confirm'}
          </Button>
        </div>
      </div>
    </Modal>
  );
}
